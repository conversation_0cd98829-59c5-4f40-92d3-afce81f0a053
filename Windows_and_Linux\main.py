import logging
import sys
import os

from WritingToolApp import WritingToolApp

# Check if we're running in console mode (when console=True in PyInstaller)
CONSOLE_MODE = hasattr(sys, 'frozen') and sys.frozen and os.name == 'nt' and sys.stdout and sys.stdout.isatty()


def _detect_mode() -> str:
    """Detect the operating mode based on the environment."""
    if not getattr(sys, "frozen", False):
        return "dev"

    base_dir = os.path.dirname(sys.executable)
    if os.path.exists(os.path.join(base_dir, "data.json")):
        return "build-final"
    if os.path.exists(os.path.join(base_dir, "data_dev.json")):
        return "build-dev"
    return "build-dev"


# Determine current mode
current_mode = _detect_mode()

# Set up logging based on mode and console state
if CONSOLE_MODE:
    # Console mode: show logs only for development modes
    if current_mode in ["dev", "build-dev"]:
        log_level = logging.DEBUG
        print("=== Writing Tools - Console Mode (Development) ===")
        print("Logs will appear in this console window.")
        print("Press Ctrl+C to exit.")
        print("=" * 50)
    else:
        # build-final in console mode: minimal logging
        log_level = logging.INFO
        print("=== Writing Tools - Console Mode ===")
        print("Press Ctrl+C to exit.")
        print("=" * 35)

    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
else:
    # Windowed mode: minimal logging for build-final, debug for dev modes
    if current_mode == "build-final":
        log_level = logging.WARNING  # Very minimal logging for production
    else:
        log_level = logging.DEBUG

    logging.basicConfig(level=log_level, format="%(asctime)s - %(levelname)s - %(message)s")


def main():
    """
    The main entry point of the application.
    """
    try:
        app = WritingToolApp(sys.argv)
        app.setQuitOnLastWindowClosed(False)

        if CONSOLE_MODE:
            logging.info("Application started in console mode")
            logging.info("Check your system tray for the Writing Tools icon")

        exit_code = app.exec()

        if CONSOLE_MODE:
            logging.info(f"Application exited with code: {exit_code}")

        sys.exit(exit_code)

    except KeyboardInterrupt:
        if CONSOLE_MODE:
            print("\nApplication interrupted by user (Ctrl+C)")
            logging.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        if CONSOLE_MODE:
            print(f"\nCritical error: {e}")
        logging.exception(f"Critical error in main: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
