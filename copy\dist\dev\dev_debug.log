2025-08-02 16:48:18,988 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 16:48:18,988 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 16:48:18,988 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 16:48:18,992 - config.settings - DEBUG -   base_dir: .
2025-08-02 16:48:18,992 - config.settings - DEBUG -   mode: dev
2025-08-02 16:48:18,993 - config.settings - DEBUG -   config_dir: config
2025-08-02 16:48:18,993 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 16:48:18,995 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:48:18,996 - config.settings - DEBUG - No settings file found at dist\dev\data_dev.json, using defaults
2025-08-02 16:48:18,998 - root - DEBUG - Unified settings loaded successfully
2025-08-02 16:48:19,077 - root - DEBUG - First launch detected (no providers configured), showing onboarding
2025-08-02 16:48:19,078 - root - DEBUG - Showing onboarding window
2025-08-02 16:48:19,078 - root - DEBUG - Initializing onboarding UI
2025-08-02 16:48:21,830 - root - DEBUG - User selected shortcut: ctrl+space, theme: gradient
2025-08-02 16:48:21,832 - config.settings - DEBUG - Saving settings:
2025-08-02 16:48:21,833 - config.settings - DEBUG -   mode: dev
2025-08-02 16:48:21,834 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:48:21,834 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:48:21,834 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:48:21,836 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:48:21,837 - config.settings - DEBUG - Saving settings:
2025-08-02 16:48:21,838 - config.settings - DEBUG -   mode: dev
2025-08-02 16:48:21,838 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:48:21,838 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:48:21,840 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:48:21,842 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:48:21,843 - root - DEBUG - Showing settings window
2025-08-02 16:48:31,149 - root - DEBUG - Creating system tray icon
2025-08-02 16:48:31,170 - root - DEBUG - Tray icon dark
2025-08-02 16:48:31,174 - root - DEBUG - Tray icon displayed
2025-08-02 16:48:31,174 - config.settings - DEBUG - Saving settings:
2025-08-02 16:48:31,177 - config.settings - DEBUG -   mode: dev
2025-08-02 16:48:31,177 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:48:31,177 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:48:31,180 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:48:31,181 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:48:31,182 - config.settings - DEBUG - Saving settings:
2025-08-02 16:48:31,183 - config.settings - DEBUG -   mode: dev
2025-08-02 16:48:31,185 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:48:31,186 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:48:31,187 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:48:31,190 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:48:31,190 - root - DEBUG - Registering hotkey
2025-08-02 16:48:31,191 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 16:48:31,204 - root - DEBUG - Hotkey registered
2025-08-02 16:48:36,666 - root - DEBUG - triggered hotkey
2025-08-02 16:48:36,666 - root - DEBUG - Hotkey pressed
2025-08-02 16:48:36,674 - root - DEBUG - Cancelling current provider's request
2025-08-02 16:48:36,674 - root - DEBUG - Showing popup window
2025-08-02 16:48:36,674 - root - DEBUG - Clipboard backup: "AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs

" (sleep: 0.2s)
2025-08-02 16:48:36,684 - root - DEBUG - Simulating Ctrl+C
2025-08-02 16:48:36,902 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 16:48:36,908 - root - DEBUG - Selected text: "AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs"
2025-08-02 16:48:36,908 - root - DEBUG - Creating new popup window
2025-08-02 16:48:36,910 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 16:48:36,912 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 16:48:36,919 - root - DEBUG - Loading actions from unified settings
2025-08-02 16:48:36,921 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,921 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,923 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,925 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,927 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,929 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,931 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,933 - root - DEBUG - DraggableButton initialized
2025-08-02 16:48:36,934 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 16:48:36,942 - root - DEBUG - Cursor is on screen: BenQ GW2790E
2025-08-02 16:48:36,942 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(-1920, 0, 1298, 730)
2025-08-02 16:48:36,984 - root - DEBUG - Popup window moved to position: (-1162, 155)
2025-08-02 16:48:45,298 - root - DEBUG - triggered hotkey
2025-08-02 16:48:45,298 - root - DEBUG - Hotkey pressed
2025-08-02 16:48:45,298 - root - DEBUG - Cancelling current provider's request
2025-08-02 16:48:45,301 - root - DEBUG - Showing popup window
2025-08-02 16:48:45,301 - root - DEBUG - Clipboard backup: "AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs

" (sleep: 0.2s)
2025-08-02 16:48:45,309 - root - DEBUG - Simulating Ctrl+C
2025-08-02 16:48:45,528 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 16:48:45,532 - root - DEBUG - No text captured, retrying with longer sleep
2025-08-02 16:48:45,535 - root - DEBUG - Clipboard backup: "AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs

" (sleep: 0.5s)
2025-08-02 16:48:45,545 - root - DEBUG - Simulating Ctrl+C
2025-08-02 16:48:46,063 - root - DEBUG - Waited 0.5s for clipboard
2025-08-02 16:48:46,068 - root - DEBUG - Selected text: ""
2025-08-02 16:48:46,070 - root - DEBUG - Existing popup window found
2025-08-02 16:48:46,072 - root - DEBUG - Creating new popup window
2025-08-02 16:48:46,074 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 16:48:46,074 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 16:48:46,080 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 16:48:46,084 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 16:48:46,086 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 16:48:46,124 - root - DEBUG - Popup window moved to position: (583, 236)
2025-08-02 16:48:49,617 - root - DEBUG - Processing option: Custom
2025-08-02 16:48:49,627 - root - DEBUG - Connecting response signals
2025-08-02 16:48:49,627 - root - DEBUG - Response signals connected
2025-08-02 16:48:49,653 - root - DEBUG - Starting processing thread for option: Custom
2025-08-02 16:48:49,657 - root - DEBUG - Getting response from provider for option: Custom
2025-08-02 16:48:49,659 - root - DEBUG - Getting response for window display
2025-08-02 16:49:28,739 - root - DEBUG - Showing settings window
2025-08-02 16:50:49,953 - config.settings - DEBUG - Saving settings:
2025-08-02 16:50:49,953 - config.settings - DEBUG -   mode: dev
2025-08-02 16:50:49,953 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:50:49,953 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:50:49,953 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:50:49,953 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:50:49,959 - config.settings - DEBUG - Saving settings:
2025-08-02 16:50:49,959 - config.settings - DEBUG -   mode: dev
2025-08-02 16:50:49,960 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:50:49,962 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:50:49,963 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:50:49,965 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:50:49,966 - config.settings - DEBUG - Saving settings:
2025-08-02 16:50:49,966 - config.settings - DEBUG -   mode: dev
2025-08-02 16:50:49,967 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:50:49,967 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:50:49,974 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:50:49,978 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:50:49,978 - config.settings - DEBUG - Saving settings:
2025-08-02 16:50:49,978 - config.settings - DEBUG -   mode: dev
2025-08-02 16:50:49,978 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:50:49,978 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:50:49,978 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:50:49,983 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:50:49,984 - root - DEBUG - Registering hotkey
2025-08-02 16:50:49,985 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 16:50:49,996 - root - DEBUG - Hotkey registered
2025-08-02 16:51:50,183 - root - DEBUG - triggered hotkey
2025-08-02 16:51:50,186 - root - DEBUG - Hotkey pressed
2025-08-02 16:51:50,186 - root - DEBUG - Cancelling current provider's request
2025-08-02 16:51:50,189 - root - DEBUG - Showing popup window
2025-08-02 16:51:50,189 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 16:51:50,195 - root - DEBUG - Simulating Ctrl+C
2025-08-02 16:51:50,415 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 16:51:50,420 - root - DEBUG - Selected text: "gemini"
2025-08-02 16:51:50,422 - root - DEBUG - Existing popup window found
2025-08-02 16:51:50,425 - root - DEBUG - Creating new popup window
2025-08-02 16:51:50,427 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 16:51:50,427 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 16:51:50,435 - root - DEBUG - Loading actions from unified settings
2025-08-02 16:51:50,437 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,439 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,442 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,442 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,442 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,442 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,449 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,451 - root - DEBUG - DraggableButton initialized
2025-08-02 16:51:50,454 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 16:51:50,461 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 16:51:50,461 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 16:51:50,533 - root - DEBUG - Popup window moved to position: (302, 183)
2025-08-02 16:51:55,463 - root - DEBUG - Processing option: Rewrite
2025-08-02 16:51:55,463 - root - DEBUG - Starting processing thread for option: Rewrite
2025-08-02 16:51:55,494 - root - DEBUG - Getting response from provider for option: Rewrite
2025-08-02 16:51:55,495 - root - DEBUG - Getting response for direct replacement
2025-08-02 16:51:57,825 - root - DEBUG - No new text to process
2025-08-02 16:51:57,831 - root - DEBUG - Response processed
2025-08-02 16:52:05,271 - root - DEBUG - triggered hotkey
2025-08-02 16:52:05,271 - root - DEBUG - Hotkey pressed
2025-08-02 16:52:05,273 - root - DEBUG - Cancelling current provider's request
2025-08-02 16:52:05,273 - root - DEBUG - Showing popup window
2025-08-02 16:52:05,275 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 16:52:05,281 - root - DEBUG - Simulating Ctrl+C
2025-08-02 16:52:05,498 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 16:52:05,504 - root - DEBUG - Selected text: "gemini"
2025-08-02 16:52:05,507 - root - DEBUG - Existing popup window found
2025-08-02 16:52:05,508 - root - DEBUG - Creating new popup window
2025-08-02 16:52:05,509 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 16:52:05,509 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 16:52:05,518 - root - DEBUG - Loading actions from unified settings
2025-08-02 16:52:05,520 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,523 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,525 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,527 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,530 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,533 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,536 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,539 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:05,545 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 16:52:05,546 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 16:52:05,546 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 16:52:05,596 - root - DEBUG - Popup window moved to position: (426, 161)
2025-08-02 16:52:10,752 - root - DEBUG - Edit mode toggled: True
2025-08-02 16:52:19,531 - config.settings - DEBUG - Saving settings:
2025-08-02 16:52:19,533 - config.settings - DEBUG -   mode: dev
2025-08-02 16:52:19,533 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:52:19,533 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:52:19,533 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:52:19,536 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:52:22,423 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 16:52:22,423 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 16:52:22,428 - root - DEBUG - Loading actions from unified settings
2025-08-02 16:52:22,428 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,431 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,434 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,435 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,437 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,438 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,442 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,444 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,446 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:22,446 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 16:52:28,919 - root - DEBUG - Edit mode toggled: True
2025-08-02 16:52:34,152 - config.settings - DEBUG - Saving settings:
2025-08-02 16:52:34,152 - config.settings - DEBUG -   mode: dev
2025-08-02 16:52:34,152 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:52:34,152 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:52:34,152 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:52:34,158 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:52:34,182 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 16:52:34,182 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 16:52:34,199 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 16:52:34,199 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 16:52:34,212 - root - DEBUG - Loading actions from unified settings
2025-08-02 16:52:34,212 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,212 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,216 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,216 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,219 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,220 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,222 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,224 - root - DEBUG - DraggableButton initialized
2025-08-02 16:52:34,226 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 16:52:38,114 - root - DEBUG - Edit mode toggled: True
2025-08-02 16:52:40,484 - config.settings - DEBUG - Saving settings:
2025-08-02 16:52:40,485 - config.settings - DEBUG -   mode: dev
2025-08-02 16:52:40,485 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 16:52:40,486 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 16:52:40,486 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 16:52:40,489 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 16:52:40,490 - root - DEBUG - Button order updated in unified settings
2025-08-02 16:52:40,492 - root - DEBUG - Drag completed with action: 2
2025-08-02 16:58:27,211 - root - DEBUG - Got response of length: 416
2025-08-02 16:58:27,211 - root - ERROR - An error occurred: 'WritingToolApp' object has no attribute 'current_response_window'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Mes_projets\WritingToolsCleanRecup\Windows_and_Linux\WritingToolApp.py", line 659, in process_option_thread
    self.current_response_window.chat_history.append(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'WritingToolApp' object has no attribute 'current_response_window'. Did you mean: 'show_response_window'?
2025-08-02 17:03:29,078 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:03:29,080 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:03:29,080 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:03:29,081 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:03:29,082 - config.settings - DEBUG -   mode: dev
2025-08-02 17:03:29,082 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:03:29,082 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:03:29,083 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:03:29,097 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:03:29,098 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:03:29,098 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:03:29,180 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:03:29,180 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:03:29,180 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:03:29,184 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:03:29,185 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:03:29,185 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:03:29,186 - root - DEBUG - Creating system tray icon
2025-08-02 17:03:29,217 - root - DEBUG - Tray icon dark
2025-08-02 17:03:29,221 - root - DEBUG - Tray icon displayed
2025-08-02 17:03:29,223 - root - DEBUG - Registering hotkey
2025-08-02 17:03:29,224 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:03:29,238 - root - DEBUG - Hotkey registered
2025-08-02 17:03:29,240 - root - DEBUG - Tray icon dark
2025-08-02 17:03:29,532 - config.settings - DEBUG - Saving settings:
2025-08-02 17:03:29,535 - config.settings - DEBUG -   mode: dev
2025-08-02 17:03:29,535 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:03:29,536 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:03:29,536 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:03:29,539 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:21:28,395 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:21:28,396 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:21:28,397 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:21:28,397 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:21:28,399 - config.settings - DEBUG -   mode: dev
2025-08-02 17:21:28,399 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:21:28,399 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:21:28,400 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:21:28,417 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:21:28,417 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:21:28,417 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:21:28,516 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:21:28,517 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:21:28,518 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:21:28,518 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:21:28,519 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:21:28,519 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:21:28,521 - root - DEBUG - Creating system tray icon
2025-08-02 17:21:28,561 - root - DEBUG - Tray icon dark
2025-08-02 17:21:28,566 - root - DEBUG - Tray icon displayed
2025-08-02 17:21:28,566 - root - DEBUG - Registering hotkey
2025-08-02 17:21:28,568 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:21:28,580 - root - DEBUG - Hotkey registered
2025-08-02 17:21:28,586 - root - DEBUG - Tray icon dark
2025-08-02 17:21:28,897 - config.settings - DEBUG - Saving settings:
2025-08-02 17:21:28,897 - config.settings - DEBUG -   mode: dev
2025-08-02 17:21:28,899 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:21:28,899 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:21:28,900 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:21:28,902 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:21:38,971 - root - DEBUG - triggered hotkey
2025-08-02 17:21:38,972 - root - DEBUG - Hotkey pressed
2025-08-02 17:21:38,972 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:21:38,976 - root - DEBUG - Showing popup window
2025-08-02 17:21:38,980 - root - DEBUG - Clipboard backup: "MUSLIM = Mossad's Undercover Strategic Level Intelligence Mission

© 1C0D, 2025

"I was a muslim" Yeah sure!" (sleep: 0.2s)
2025-08-02 17:21:38,990 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:21:39,209 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:21:39,213 - root - DEBUG - No text captured, retrying with longer sleep
2025-08-02 17:21:39,217 - root - DEBUG - Clipboard backup: "MUSLIM = Mossad's Undercover Strategic Level Intelligence Mission

© 1C0D, 2025

"I was a muslim" Yeah sure!" (sleep: 0.5s)
2025-08-02 17:21:39,230 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:21:39,749 - root - DEBUG - Waited 0.5s for clipboard
2025-08-02 17:21:39,756 - root - DEBUG - Selected text: ""
2025-08-02 17:21:39,758 - root - DEBUG - Creating new popup window
2025-08-02 17:21:39,761 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:21:39,763 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:21:39,782 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:21:39,792 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:21:39,794 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:21:39,896 - root - DEBUG - Popup window moved to position: (486, 460)
2025-08-02 17:21:45,296 - root - DEBUG - Processing option: Custom
2025-08-02 17:21:45,309 - root - DEBUG - Connecting response signals
2025-08-02 17:21:45,311 - root - DEBUG - Response signals connected
2025-08-02 17:21:45,338 - root - DEBUG - Starting processing thread for option: Custom
2025-08-02 17:21:45,346 - root - DEBUG - Getting response from provider for option: Custom
2025-08-02 17:21:45,346 - root - DEBUG - Getting response for window display
2025-08-02 17:21:48,429 - root - DEBUG - Got response of length: 535
2025-08-02 17:21:48,429 - root - DEBUG - Invoked set_text on response window
2025-08-02 17:22:23,091 - root - DEBUG - triggered hotkey
2025-08-02 17:22:23,092 - root - DEBUG - Hotkey pressed
2025-08-02 17:22:23,093 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:22:23,093 - root - DEBUG - Showing popup window
2025-08-02 17:22:23,094 - root - DEBUG - Clipboard backup: "MUSLIM = Mossad's Undercover Strategic Level Intelligence Mission

© 1C0D, 2025

"I was a muslim" Yeah sure!" (sleep: 0.2s)
2025-08-02 17:22:23,102 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:22:23,321 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:22:23,329 - root - DEBUG - Selected text: "le roi"
2025-08-02 17:22:23,331 - root - DEBUG - Existing popup window found
2025-08-02 17:22:23,333 - root - DEBUG - Creating new popup window
2025-08-02 17:22:23,338 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:22:23,341 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:22:23,351 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:22:23,431 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,435 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,437 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,439 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,442 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,442 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,445 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,446 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:23,449 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:22:23,451 - root - DEBUG - Cursor is on screen: BenQ GW2790E
2025-08-02 17:22:23,452 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(-1920, 0, 1298, 730)
2025-08-02 17:22:23,498 - root - DEBUG - Popup window moved to position: (-893, 400)
2025-08-02 17:22:46,155 - root - DEBUG - triggered hotkey
2025-08-02 17:22:46,156 - root - DEBUG - Hotkey pressed
2025-08-02 17:22:46,157 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:22:46,158 - root - DEBUG - Showing popup window
2025-08-02 17:22:46,159 - root - DEBUG - Clipboard backup: "MUSLIM = Mossad's Undercover Strategic Level Intelligence Mission

© 1C0D, 2025

"I was a muslim" Yeah sure!" (sleep: 0.2s)
2025-08-02 17:22:46,173 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:22:46,397 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:22:46,404 - root - DEBUG - Selected text: "le roi"
2025-08-02 17:22:46,404 - root - DEBUG - Existing popup window found
2025-08-02 17:22:46,406 - root - DEBUG - Creating new popup window
2025-08-02 17:22:46,407 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:22:46,407 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:22:46,414 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:22:46,415 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,417 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,419 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,420 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,421 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,423 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,425 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,426 - root - DEBUG - DraggableButton initialized
2025-08-02 17:22:46,430 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:22:46,436 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:22:46,436 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:22:46,482 - root - DEBUG - Popup window moved to position: (9, 74)
2025-08-02 17:24:10,856 - root - DEBUG - Stopping the listener
2025-08-02 17:24:10,857 - root - DEBUG - Exiting application
2025-08-02 17:26:32,951 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:26:32,952 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:26:32,952 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:26:32,954 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:26:32,954 - config.settings - DEBUG -   mode: dev
2025-08-02 17:26:32,955 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:26:32,955 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:26:32,956 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:26:32,958 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:26:32,958 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:26:32,959 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:26:33,033 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:26:33,033 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:26:33,034 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:26:33,034 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:26:33,034 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:26:33,035 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:26:33,035 - root - DEBUG - Creating system tray icon
2025-08-02 17:26:33,066 - root - DEBUG - Tray icon dark
2025-08-02 17:26:33,071 - root - DEBUG - Tray icon displayed
2025-08-02 17:26:33,071 - root - DEBUG - Registering hotkey
2025-08-02 17:26:33,072 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:26:33,084 - root - DEBUG - Hotkey registered
2025-08-02 17:26:33,086 - root - DEBUG - Tray icon dark
2025-08-02 17:26:33,502 - config.settings - DEBUG - Saving settings:
2025-08-02 17:26:33,502 - config.settings - DEBUG -   mode: dev
2025-08-02 17:26:33,504 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:26:33,504 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:26:33,504 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:26:33,507 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:27:12,402 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:27:12,402 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:27:12,402 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:27:12,405 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:27:12,405 - config.settings - DEBUG -   mode: dev
2025-08-02 17:27:12,406 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:27:12,407 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:27:12,407 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:27:12,410 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:27:12,410 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:27:12,410 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:27:12,491 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:27:12,493 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:27:12,494 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:27:12,494 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:27:12,494 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:27:12,495 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:27:12,495 - root - DEBUG - Creating system tray icon
2025-08-02 17:27:12,529 - root - DEBUG - Tray icon dark
2025-08-02 17:27:12,535 - root - DEBUG - Tray icon displayed
2025-08-02 17:27:12,535 - root - DEBUG - Registering hotkey
2025-08-02 17:27:12,536 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:27:12,544 - root - DEBUG - Hotkey registered
2025-08-02 17:27:12,551 - root - DEBUG - Tray icon dark
2025-08-02 17:27:12,716 - config.settings - DEBUG - Saving settings:
2025-08-02 17:27:12,716 - config.settings - DEBUG -   mode: dev
2025-08-02 17:27:12,716 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:27:12,716 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:27:12,716 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:27:12,716 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:27:20,050 - root - DEBUG - triggered hotkey
2025-08-02 17:27:20,058 - root - DEBUG - Hotkey pressed
2025-08-02 17:27:20,060 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:27:20,062 - root - DEBUG - Showing popup window
2025-08-02 17:27:20,064 - root - DEBUG - Clipboard backup: "Mossad's Undercover Strategic Level Intelligence Mission" (sleep: 0.2s)
2025-08-02 17:27:20,075 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:27:20,294 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:27:20,297 - root - DEBUG - Selected text: "le roi"
2025-08-02 17:27:20,301 - root - DEBUG - Creating new popup window
2025-08-02 17:27:20,303 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:27:20,306 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:27:20,347 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:27:20,347 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,350 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,351 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,352 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,353 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,356 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,357 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,358 - root - DEBUG - DraggableButton initialized
2025-08-02 17:27:20,362 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:27:20,367 - root - DEBUG - Cursor is on screen: BenQ GW2790E
2025-08-02 17:27:20,369 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(-1920, 0, 1298, 730)
2025-08-02 17:27:20,466 - root - DEBUG - Popup window moved to position: (-893, 218)
2025-08-02 17:27:38,461 - root - DEBUG - Entering edit mode
2025-08-02 17:27:44,780 - config.settings - DEBUG - Saving settings:
2025-08-02 17:27:44,781 - config.settings - DEBUG -   mode: dev
2025-08-02 17:27:44,782 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:27:44,783 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:27:44,783 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:27:44,785 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:27:44,785 - root - DEBUG - Button order updated in unified settings
2025-08-02 17:27:44,788 - root - DEBUG - Drag completed with action: 2
2025-08-02 17:27:58,200 - root - DEBUG - Exiting edit mode
2025-08-02 17:28:06,089 - root - DEBUG - Entering edit mode
2025-08-02 17:28:09,393 - config.settings - DEBUG - Saving settings:
2025-08-02 17:28:09,393 - config.settings - DEBUG -   mode: dev
2025-08-02 17:28:09,393 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:28:09,393 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:28:09,393 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:28:09,393 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:28:09,393 - root - DEBUG - Button order updated in unified settings
2025-08-02 17:28:09,399 - root - DEBUG - Drag completed with action: 2
2025-08-02 17:28:21,894 - root - DEBUG - Resetting to default actions from DEFAULT_ACTIONS
2025-08-02 17:28:21,895 - config.settings - DEBUG - Saving settings:
2025-08-02 17:28:21,895 - config.settings - DEBUG -   mode: dev
2025-08-02 17:28:21,897 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:28:21,898 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:28:21,899 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:28:21,902 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:28:21,903 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:28:21,905 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,906 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,908 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,910 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,912 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,912 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,914 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:21,917 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:36,581 - root - DEBUG - Exiting edit mode
2025-08-02 17:28:43,358 - root - DEBUG - triggered hotkey
2025-08-02 17:28:43,359 - root - DEBUG - Hotkey pressed
2025-08-02 17:28:43,361 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:28:43,362 - root - DEBUG - Showing popup window
2025-08-02 17:28:43,362 - root - DEBUG - Clipboard backup: "Mossad's Undercover Strategic Level Intelligence Mission" (sleep: 0.2s)
2025-08-02 17:28:43,371 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:28:43,590 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:28:43,596 - root - DEBUG - No text captured, retrying with longer sleep
2025-08-02 17:28:43,599 - root - DEBUG - Clipboard backup: "Mossad's Undercover Strategic Level Intelligence Mission" (sleep: 0.5s)
2025-08-02 17:28:43,610 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:28:44,127 - root - DEBUG - Waited 0.5s for clipboard
2025-08-02 17:28:44,135 - root - DEBUG - Selected text: ""
2025-08-02 17:28:44,137 - root - DEBUG - Existing popup window found
2025-08-02 17:28:44,138 - root - DEBUG - Creating new popup window
2025-08-02 17:28:44,139 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:28:44,139 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:28:44,148 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:28:44,151 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:28:44,152 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:28:44,201 - root - DEBUG - Popup window moved to position: (128, 365)
2025-08-02 17:28:52,300 - root - DEBUG - triggered hotkey
2025-08-02 17:28:52,301 - root - DEBUG - Hotkey pressed
2025-08-02 17:28:52,301 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:28:52,304 - root - DEBUG - Showing popup window
2025-08-02 17:28:52,304 - root - DEBUG - Clipboard backup: "Mossad's Undercover Strategic Level Intelligence Mission" (sleep: 0.2s)
2025-08-02 17:28:52,315 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:28:52,533 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:28:52,542 - root - DEBUG - Selected text: "le roi"
2025-08-02 17:28:52,543 - root - DEBUG - Existing popup window found
2025-08-02 17:28:52,544 - root - DEBUG - Creating new popup window
2025-08-02 17:28:52,546 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:28:52,546 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:28:52,551 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:28:52,551 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,551 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,558 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,560 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,560 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,563 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,564 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,567 - root - DEBUG - DraggableButton initialized
2025-08-02 17:28:52,570 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:28:52,572 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:28:52,575 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:28:52,619 - root - DEBUG - Popup window moved to position: (0, 207)
2025-08-02 17:28:57,479 - root - DEBUG - Entering edit mode
2025-08-02 17:29:01,693 - config.settings - DEBUG - Saving settings:
2025-08-02 17:29:01,693 - config.settings - DEBUG -   mode: dev
2025-08-02 17:29:01,693 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:29:01,693 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:29:01,693 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:29:01,699 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:29:01,699 - root - DEBUG - Button order updated in unified settings
2025-08-02 17:29:01,701 - root - DEBUG - Drag completed with action: 2
2025-08-02 17:29:10,228 - root - DEBUG - Exiting edit mode
2025-08-02 17:29:17,219 - root - DEBUG - Entering edit mode
2025-08-02 17:29:20,131 - root - DEBUG - Exiting edit mode
2025-08-02 17:29:24,087 - root - DEBUG - triggered hotkey
2025-08-02 17:29:24,087 - root - DEBUG - Hotkey pressed
2025-08-02 17:29:24,089 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:29:24,090 - root - DEBUG - Showing popup window
2025-08-02 17:29:24,091 - root - DEBUG - Clipboard backup: "Mossad's Undercover Strategic Level Intelligence Mission" (sleep: 0.2s)
2025-08-02 17:29:24,100 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:29:24,317 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:29:24,325 - root - DEBUG - Selected text: "le roi"
2025-08-02 17:29:24,325 - root - DEBUG - Existing popup window found
2025-08-02 17:29:24,328 - root - DEBUG - Creating new popup window
2025-08-02 17:29:24,330 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:29:24,332 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:29:24,342 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:29:24,348 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,350 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,351 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,378 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,381 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,383 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,385 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,404 - root - DEBUG - DraggableButton initialized
2025-08-02 17:29:24,409 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:29:24,414 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:29:24,417 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:29:24,479 - root - DEBUG - Popup window moved to position: (217, 296)
2025-08-02 17:29:40,503 - root - DEBUG - Entering edit mode
2025-08-02 17:29:47,652 - root - DEBUG - Exiting edit mode
2025-08-02 17:29:53,222 - root - DEBUG - Entering edit mode
2025-08-02 17:31:14,674 - config.settings - DEBUG - Saving settings:
2025-08-02 17:31:14,676 - config.settings - DEBUG -   mode: dev
2025-08-02 17:31:14,676 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:31:14,676 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:31:14,676 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:31:14,680 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:31:14,680 - root - DEBUG - Button order updated in unified settings
2025-08-02 17:31:14,682 - root - DEBUG - Drag completed with action: 2
2025-08-02 17:31:22,182 - root - DEBUG - Exiting edit mode
2025-08-02 17:31:28,581 - root - DEBUG - Stopping the listener
2025-08-02 17:31:28,583 - root - DEBUG - Exiting application
2025-08-02 17:36:16,637 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:36:16,637 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:36:16,637 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:36:16,637 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:36:16,640 - config.settings - DEBUG -   mode: dev
2025-08-02 17:36:16,641 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:36:16,642 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:36:16,643 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:36:16,663 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:36:16,663 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:36:16,665 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:36:16,743 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:36:16,743 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:36:16,743 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:36:16,745 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:36:16,745 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:36:16,745 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:36:16,747 - root - DEBUG - Creating system tray icon
2025-08-02 17:36:16,778 - root - DEBUG - Tray icon dark
2025-08-02 17:36:16,780 - root - DEBUG - Tray icon displayed
2025-08-02 17:36:16,780 - root - DEBUG - Registering hotkey
2025-08-02 17:36:16,780 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:36:16,794 - root - DEBUG - Hotkey registered
2025-08-02 17:36:16,794 - root - DEBUG - Tray icon dark
2025-08-02 17:36:16,967 - config.settings - DEBUG - Saving settings:
2025-08-02 17:36:16,967 - config.settings - DEBUG -   mode: dev
2025-08-02 17:36:16,967 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:36:16,967 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:36:16,983 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:36:16,986 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:36:20,707 - root - DEBUG - triggered hotkey
2025-08-02 17:36:20,707 - root - DEBUG - Hotkey pressed
2025-08-02 17:36:20,709 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:36:20,710 - root - DEBUG - Showing popup window
2025-08-02 17:36:20,711 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 17:36:20,719 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:36:20,736 - root - INFO - Received SIGINT. Exiting...
2025-08-02 17:36:20,737 - root - DEBUG - Stopping the listener
2025-08-02 17:36:20,737 - root - DEBUG - Exiting application
2025-08-02 17:36:20,943 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:36:20,950 - root - DEBUG - No text captured, retrying with longer sleep
2025-08-02 17:36:20,952 - root - DEBUG - Clipboard backup: "" (sleep: 0.5s)
2025-08-02 17:36:20,964 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:36:20,980 - root - INFO - Received SIGINT. Exiting...
2025-08-02 17:36:20,980 - root - DEBUG - Stopping the listener
2025-08-02 17:36:20,980 - root - DEBUG - Exiting application
2025-08-02 17:36:21,485 - root - DEBUG - Waited 0.5s for clipboard
2025-08-02 17:36:21,495 - root - DEBUG - Selected text: ""
2025-08-02 17:36:21,496 - root - DEBUG - Creating new popup window
2025-08-02 17:36:21,496 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:36:21,498 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:36:21,514 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:36:21,525 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:36:21,525 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:36:21,616 - root - DEBUG - Popup window moved to position: (48, 179)
2025-08-02 17:36:46,642 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:36:46,642 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:36:46,642 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:36:46,642 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:36:46,642 - config.settings - DEBUG -   mode: dev
2025-08-02 17:36:46,642 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:36:46,642 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:36:46,653 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:36:46,655 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:36:46,656 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:36:46,657 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:36:46,737 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:36:46,737 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:36:46,737 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:36:46,737 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:36:46,737 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:36:46,737 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:36:46,740 - root - DEBUG - Creating system tray icon
2025-08-02 17:36:46,771 - root - DEBUG - Tray icon dark
2025-08-02 17:36:46,777 - root - DEBUG - Tray icon displayed
2025-08-02 17:36:46,779 - root - DEBUG - Registering hotkey
2025-08-02 17:36:46,779 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:36:46,829 - root - DEBUG - Hotkey registered
2025-08-02 17:36:46,832 - root - DEBUG - Tray icon dark
2025-08-02 17:36:47,007 - config.settings - DEBUG - Saving settings:
2025-08-02 17:36:47,022 - config.settings - DEBUG -   mode: dev
2025-08-02 17:36:47,023 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:36:47,023 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:36:47,023 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:36:47,023 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:36:49,542 - root - DEBUG - triggered hotkey
2025-08-02 17:36:49,542 - root - DEBUG - Hotkey pressed
2025-08-02 17:36:49,542 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:36:49,547 - root - DEBUG - Showing popup window
2025-08-02 17:36:49,549 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 17:36:49,557 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:36:49,767 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:36:49,776 - root - DEBUG - Selected text: "gemini"
2025-08-02 17:36:49,779 - root - DEBUG - Creating new popup window
2025-08-02 17:36:49,781 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:36:49,782 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:36:49,796 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:36:49,796 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,796 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,796 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,807 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,808 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,811 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,812 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,814 - root - DEBUG - DraggableButton initialized
2025-08-02 17:36:49,815 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:36:49,826 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:36:49,828 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:36:49,960 - root - DEBUG - Popup window moved to position: (28, 180)
2025-08-02 17:39:11,756 - root - DEBUG - triggered hotkey
2025-08-02 17:39:11,756 - root - DEBUG - Hotkey pressed
2025-08-02 17:39:11,758 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:39:11,759 - root - DEBUG - Showing popup window
2025-08-02 17:39:11,760 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 17:39:11,768 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:39:11,985 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:39:11,992 - root - DEBUG - No text captured, retrying with longer sleep
2025-08-02 17:39:11,995 - root - DEBUG - Clipboard backup: "" (sleep: 0.5s)
2025-08-02 17:39:12,008 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:39:12,530 - root - DEBUG - Waited 0.5s for clipboard
2025-08-02 17:39:12,538 - root - DEBUG - Selected text: ""
2025-08-02 17:39:12,538 - root - DEBUG - Existing popup window found
2025-08-02 17:39:12,544 - root - DEBUG - Creating new popup window
2025-08-02 17:39:12,552 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:39:12,553 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:39:12,561 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:39:12,570 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:39:12,572 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:39:12,615 - root - DEBUG - Popup window moved to position: (162, 216)
2025-08-02 17:39:17,921 - root - DEBUG - triggered hotkey
2025-08-02 17:39:17,924 - root - DEBUG - Hotkey pressed
2025-08-02 17:39:17,925 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:39:17,925 - root - DEBUG - Showing popup window
2025-08-02 17:39:17,925 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 17:39:17,927 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:39:18,156 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:39:18,160 - root - DEBUG - Selected text: "gemini"
2025-08-02 17:39:18,164 - root - DEBUG - Existing popup window found
2025-08-02 17:39:18,167 - root - DEBUG - Creating new popup window
2025-08-02 17:39:18,168 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:39:18,170 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:39:18,179 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:39:18,181 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,183 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,183 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,186 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,186 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,190 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,192 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,194 - root - DEBUG - DraggableButton initialized
2025-08-02 17:39:18,195 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:39:18,201 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:39:18,201 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:39:18,247 - root - DEBUG - Popup window moved to position: (35, 175)
2025-08-02 17:40:04,008 - root - DEBUG - triggered hotkey
2025-08-02 17:40:04,010 - root - DEBUG - Hotkey pressed
2025-08-02 17:40:04,010 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:40:04,010 - root - DEBUG - Showing popup window
2025-08-02 17:40:04,010 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 17:40:04,021 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:40:04,240 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:40:04,249 - root - DEBUG - Selected text: "gemini"
2025-08-02 17:40:04,249 - root - DEBUG - Existing popup window found
2025-08-02 17:40:04,254 - root - DEBUG - Creating new popup window
2025-08-02 17:40:04,255 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:40:04,259 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:40:04,268 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:40:04,268 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,272 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,274 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,276 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,284 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,286 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,288 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,290 - root - DEBUG - DraggableButton initialized
2025-08-02 17:40:04,293 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:40:04,300 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:40:04,302 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:40:04,348 - root - DEBUG - Popup window moved to position: (39, 171)
2025-08-02 17:40:12,480 - root - DEBUG - Entering edit mode
2025-08-02 17:42:41,655 - config.settings - DEBUG - Saving settings:
2025-08-02 17:42:41,655 - config.settings - DEBUG -   mode: dev
2025-08-02 17:42:41,655 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:42:41,655 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:42:41,655 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:42:41,658 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:42:41,658 - root - DEBUG - Button order updated in unified settings
2025-08-02 17:42:41,660 - root - DEBUG - Drag completed with action: 2
2025-08-02 17:43:59,449 - root - DEBUG - Exiting edit mode
2025-08-02 17:44:14,764 - root - DEBUG - triggered hotkey
2025-08-02 17:44:14,774 - root - DEBUG - Hotkey pressed
2025-08-02 17:44:14,774 - root - DEBUG - Cancelling current provider's request
2025-08-02 17:44:14,774 - root - DEBUG - Showing popup window
2025-08-02 17:44:14,774 - root - DEBUG - Clipboard backup: "" (sleep: 0.2s)
2025-08-02 17:44:14,785 - root - DEBUG - Simulating Ctrl+C
2025-08-02 17:44:15,003 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 17:44:15,011 - root - DEBUG - Selected text: "gemini"
2025-08-02 17:44:15,017 - root - DEBUG - Existing popup window found
2025-08-02 17:44:15,018 - root - DEBUG - Creating new popup window
2025-08-02 17:44:15,019 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 17:44:15,022 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 17:44:15,030 - root - DEBUG - Loading actions from unified settings
2025-08-02 17:44:15,031 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,032 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,032 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,037 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,038 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,038 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,042 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,042 - root - DEBUG - DraggableButton initialized
2025-08-02 17:44:15,048 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 17:44:15,053 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 17:44:15,053 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 17:44:15,100 - root - DEBUG - Popup window moved to position: (43, 178)
2025-08-02 17:44:21,780 - root - DEBUG - Entering edit mode
2025-08-02 17:44:23,740 - config.settings - DEBUG - Saving settings:
2025-08-02 17:44:23,740 - config.settings - DEBUG -   mode: dev
2025-08-02 17:44:23,741 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:44:23,741 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:44:23,741 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:44:23,741 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 17:44:23,745 - root - DEBUG - Button order updated in unified settings
2025-08-02 17:44:23,745 - root - DEBUG - Drag completed with action: 2
2025-08-02 17:44:40,848 - root - DEBUG - Exiting edit mode
2025-08-02 17:46:31,466 - root - DEBUG - Stopping the listener
2025-08-02 17:46:31,467 - root - DEBUG - Exiting application
2025-08-02 17:51:30,770 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 17:51:30,770 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 17:51:30,770 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 17:51:30,772 - config.settings - DEBUG -   base_dir: .
2025-08-02 17:51:30,772 - config.settings - DEBUG -   mode: dev
2025-08-02 17:51:30,773 - config.settings - DEBUG -   config_dir: config
2025-08-02 17:51:30,773 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 17:51:30,774 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:51:30,776 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 17:51:30,776 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 17:51:30,777 - root - DEBUG - Unified settings loaded successfully
2025-08-02 17:51:30,852 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 17:51:30,852 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 17:51:30,852 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 17:51:30,852 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 17:51:30,852 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 17:51:30,852 - root - DEBUG - Provider config loaded successfully
2025-08-02 17:51:30,852 - root - DEBUG - Creating system tray icon
2025-08-02 17:51:30,887 - root - DEBUG - Tray icon dark
2025-08-02 17:51:30,892 - root - DEBUG - Tray icon displayed
2025-08-02 17:51:30,893 - root - DEBUG - Registering hotkey
2025-08-02 17:51:30,894 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 17:51:30,905 - root - DEBUG - Hotkey registered
2025-08-02 17:51:30,907 - root - DEBUG - Tray icon dark
2025-08-02 17:51:31,240 - config.settings - DEBUG - Saving settings:
2025-08-02 17:51:31,241 - config.settings - DEBUG -   mode: dev
2025-08-02 17:51:31,241 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 17:51:31,241 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 17:51:31,241 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 17:51:31,245 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 22:10:45,156 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 22:10:45,158 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 22:10:45,159 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 22:10:45,160 - config.settings - DEBUG -   base_dir: .
2025-08-02 22:10:45,162 - config.settings - DEBUG -   mode: dev
2025-08-02 22:10:45,162 - config.settings - DEBUG -   config_dir: config
2025-08-02 22:10:45,164 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 22:10:45,165 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 22:10:45,173 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 22:10:45,174 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 22:10:45,174 - root - DEBUG - Unified settings loaded successfully
2025-08-02 22:10:45,343 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 22:10:45,343 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 22:10:45,344 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 22:10:45,344 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 22:10:45,345 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 22:10:45,347 - root - DEBUG - Provider config loaded successfully
2025-08-02 22:10:45,348 - root - DEBUG - Creating system tray icon
2025-08-02 23:58:30,181 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-02 23:58:30,182 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-02 23:58:30,182 - config.settings - DEBUG - SettingsManager initialized:
2025-08-02 23:58:30,183 - config.settings - DEBUG -   base_dir: .
2025-08-02 23:58:30,183 - config.settings - DEBUG -   mode: dev
2025-08-02 23:58:30,185 - config.settings - DEBUG -   config_dir: config
2025-08-02 23:58:30,185 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-02 23:58:30,185 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 23:58:30,188 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-02 23:58:30,189 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-02 23:58:30,189 - root - DEBUG - Unified settings loaded successfully
2025-08-02 23:58:30,354 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-02 23:58:30,354 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-02 23:58:30,354 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-02 23:58:30,354 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-02 23:58:30,354 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-02 23:58:30,354 - root - DEBUG - Provider config loaded successfully
2025-08-02 23:58:30,354 - root - DEBUG - Creating system tray icon
2025-08-02 23:58:30,406 - root - DEBUG - Tray icon dark
2025-08-02 23:58:30,406 - root - DEBUG - Tray icon displayed
2025-08-02 23:58:30,412 - root - DEBUG - Registering hotkey
2025-08-02 23:58:30,414 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-02 23:58:30,429 - root - DEBUG - Hotkey registered
2025-08-02 23:58:30,435 - root - DEBUG - Tray icon dark
2025-08-02 23:58:30,845 - config.settings - DEBUG - Saving settings:
2025-08-02 23:58:30,845 - config.settings - DEBUG -   mode: dev
2025-08-02 23:58:30,849 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-02 23:58:30,849 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-02 23:58:30,850 - config.settings - DEBUG - Created directory: dist\dev
2025-08-02 23:58:30,851 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-02 23:59:33,849 - root - DEBUG - triggered hotkey
2025-08-02 23:59:33,849 - root - DEBUG - Hotkey pressed
2025-08-02 23:59:33,849 - root - DEBUG - Cancelling current provider's request
2025-08-02 23:59:33,851 - root - DEBUG - Showing popup window
2025-08-02 23:59:33,853 - root - DEBUG - Clipboard backup: "self.output_ready_signal.connect(self.replace_text)" (sleep: 0.2s)
2025-08-02 23:59:33,858 - root - DEBUG - Simulating Ctrl+C
2025-08-02 23:59:34,067 - root - DEBUG - Waited 0.2s for clipboard
2025-08-02 23:59:34,067 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth and power, has long been at the center of global financial narratives and conspiracy theories, true and not. Emerging from 18th-century Frankfurt, their banking empire shaped European and American economies, but a longer historical view reveals a millennia-hidden story, supported by modern DNA, that the Rothschilds are not Jewish but Babylonian Rādhānites (Turkic, Persian, Khazarian, Sogdian, Chinese)."
2025-08-02 23:59:34,071 - root - DEBUG - Creating new popup window
2025-08-02 23:59:34,071 - root - DEBUG - Initializing CustomPopupWindow
2025-08-02 23:59:34,071 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-02 23:59:34,106 - root - DEBUG - Loading actions from unified settings
2025-08-02 23:59:34,109 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,110 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,110 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,110 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,114 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,114 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,117 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,118 - root - DEBUG - DraggableButton initialized
2025-08-02 23:59:34,127 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-02 23:59:34,140 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-02 23:59:34,142 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-02 23:59:34,242 - root - DEBUG - Popup window moved to position: (716, 359)
2025-08-02 23:59:39,693 - root - DEBUG - Processing option: Summary
2025-08-02 23:59:39,706 - root - DEBUG - Connecting response signals
2025-08-02 23:59:39,706 - root - DEBUG - Response signals connected
2025-08-02 23:59:39,727 - root - DEBUG - Starting processing thread for option: Summary
2025-08-02 23:59:39,732 - root - DEBUG - Getting response from provider for option: Summary
2025-08-02 23:59:39,732 - root - DEBUG - Getting response for window display
2025-08-02 23:59:41,404 - root - DEBUG - Got response of length: 490
2025-08-02 23:59:41,404 - root - DEBUG - Invoked set_text on response window
2025-08-03 00:00:27,888 - root - DEBUG - triggered hotkey
2025-08-03 00:00:27,891 - root - DEBUG - Hotkey pressed
2025-08-03 00:00:27,891 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:00:27,892 - root - DEBUG - Showing popup window
2025-08-03 00:00:27,892 - root - DEBUG - Clipboard backup: "self.output_ready_signal.connect(self.replace_text)" (sleep: 0.2s)
2025-08-03 00:00:27,895 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:00:28,104 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:00:28,108 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth and power, has long been at the center of global financial narratives and conspiracy theories, true and not. Emerging from 18th-century Frankfurt, their banking empire shaped European and American economies, but a longer historical view reveals a millennia-hidden story, supported by modern DNA, that the Rothschilds are not Jewish but Babylonian Rādhānites (Turkic, Persian, Khazarian, Sogdian, Chinese)."
2025-08-03 00:00:28,109 - root - DEBUG - Existing popup window found
2025-08-03 00:00:28,109 - root - DEBUG - Creating new popup window
2025-08-03 00:00:28,111 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:00:28,111 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:00:28,120 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:00:28,120 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,120 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,123 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,125 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,127 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,127 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,127 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,127 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:28,133 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:00:28,135 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:00:28,136 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:00:28,157 - root - DEBUG - Popup window moved to position: (602, 359)
2025-08-03 00:00:34,200 - root - DEBUG - Processing option: Concise
2025-08-03 00:00:34,200 - root - DEBUG - Starting processing thread for option: Concise
2025-08-03 00:00:34,219 - root - DEBUG - Getting response from provider for option: Concise
2025-08-03 00:00:34,219 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:00:39,115 - root - DEBUG - No new text to process
2025-08-03 00:00:39,115 - root - DEBUG - No new text to process
2025-08-03 00:00:39,115 - root - DEBUG - Response processed
2025-08-03 00:00:44,323 - root - DEBUG - triggered hotkey
2025-08-03 00:00:44,323 - root - DEBUG - Hotkey pressed
2025-08-03 00:00:44,323 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:00:44,323 - root - DEBUG - Showing popup window
2025-08-03 00:00:44,328 - root - DEBUG - Clipboard backup: "self.output_ready_signal.connect(self.replace_text)" (sleep: 0.2s)
2025-08-03 00:00:44,331 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:00:44,539 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:00:44,541 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth and power, has long been at the center of global financial narratives and conspiracy theories, true and not. Emerging from 18th-century Frankfurt, their banking empire shaped European and American economies, but a longer historical view reveals a millennia-hidden story, supported by modern DNA, that the Rothschilds are not Jewish but Babylonian Rādhānites (Turkic, Persian, Khazarian, Sogdian, Chinese)"
2025-08-03 00:00:44,541 - root - DEBUG - Existing popup window found
2025-08-03 00:00:44,545 - root - DEBUG - Creating new popup window
2025-08-03 00:00:44,545 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:00:44,547 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:00:44,556 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:00:44,557 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,557 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,558 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,558 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,561 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,561 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,564 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,566 - root - DEBUG - DraggableButton initialized
2025-08-03 00:00:44,570 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:00:44,570 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:00:44,572 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:00:44,604 - root - DEBUG - Popup window moved to position: (573, 318)
2025-08-03 00:00:51,186 - root - DEBUG - Processing option: Concise
2025-08-03 00:00:51,187 - root - DEBUG - Starting processing thread for option: Concise
2025-08-03 00:00:51,204 - root - DEBUG - Getting response from provider for option: Concise
2025-08-03 00:00:51,204 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:00:56,358 - root - DEBUG - No new text to process
2025-08-03 00:00:56,358 - root - DEBUG - No new text to process
2025-08-03 00:00:56,358 - root - DEBUG - Response processed
2025-08-03 00:01:05,696 - root - DEBUG - triggered hotkey
2025-08-03 00:01:05,696 - root - DEBUG - Hotkey pressed
2025-08-03 00:01:05,696 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:01:05,699 - root - DEBUG - Showing popup window
2025-08-03 00:01:05,700 - root - DEBUG - Clipboard backup: "self.output_ready_signal.connect(self.replace_text)" (sleep: 0.2s)
2025-08-03 00:01:05,700 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:01:05,911 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:01:05,911 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth and power, has long been at the center of global financial narratives and conspiracy theories, true and not. Emerging from 18th-century Frankfurt, their banking empire shaped European and American economies, but a longer historical view reveals a millennia-hidden story, supported by modern DNA, that the Rothschilds are not Jewish but Babylonian Rādhānites (Turkic, Persian, Khazarian, Sogdian, Chinese)."
2025-08-03 00:01:05,911 - root - DEBUG - Existing popup window found
2025-08-03 00:01:05,911 - root - DEBUG - Creating new popup window
2025-08-03 00:01:05,917 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:01:05,917 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:01:05,925 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:01:05,926 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,926 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,926 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,926 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,926 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,933 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,937 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,937 - root - DEBUG - DraggableButton initialized
2025-08-03 00:01:05,940 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:01:05,941 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:01:05,941 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:01:05,966 - root - DEBUG - Popup window moved to position: (492, 366)
2025-08-03 00:01:15,559 - root - DEBUG - Processing option: Custom
2025-08-03 00:01:15,559 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:01:15,578 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:01:15,581 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:01:16,736 - root - DEBUG - No new text to process
2025-08-03 00:01:16,736 - root - DEBUG - Processing output text
2025-08-03 00:01:16,736 - root - DEBUG - Response processed
2025-08-03 00:01:16,736 - root - DEBUG - Clipboard backup: "self.output_ready_signal.connect(self.replace_text)" (sleep: 0.1s)
2025-08-03 00:01:16,736 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:01:16,850 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:01:17,063 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, a longtemps été au centre des récits financiers mondiaux et des théories du complot, vraies ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une vue historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, persans, khazars, sogdiens, chinois)." (sleep: 0.1s)
2025-08-03 00:01:17,066 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:01:17,176 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:01:17,181 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:01:17,183 - root - DEBUG - Showing non-editable modal window
2025-08-03 00:01:17,344 - root - ERROR - Error showing non-editable modal: 'PySide6.QtWidgets.QDialog.__init__' called with wrong argument types:
  PySide6.QtWidgets.QDialog.__init__(WritingToolApp)
Supported signatures:
  PySide6.QtWidgets.QDialog.__init__(parent: PySide6.QtWidgets.QWidget | None = None, f: PySide6.QtCore.Qt.WindowType = Default(Qt.WindowFlags), *, sizeGripEnabled: bool | None = None, modal: bool | None = None)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Mes_projets\WritingToolsCleanRecup\Windows_and_Linux\WritingToolApp.py", line 837, in _show_non_editable_modal
    self.non_editable_modal = ui.NonEditableModal.NonEditableModal(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\Mes_projets\WritingToolsCleanRecup\Windows_and_Linux\ui\NonEditableModal.py", line 24, in __init__
    super().__init__(parent)
TypeError: 'PySide6.QtWidgets.QDialog.__init__' called with wrong argument types:
  PySide6.QtWidgets.QDialog.__init__(WritingToolApp)
Supported signatures:
  PySide6.QtWidgets.QDialog.__init__(parent: PySide6.QtWidgets.QWidget | None = None, f: PySide6.QtCore.Qt.WindowType = Default(Qt.WindowFlags), *, sizeGripEnabled: bool | None = None, modal: bool | None = None)
2025-08-03 00:08:48,736 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:08:48,737 - root - DEBUG - Stopping the listener
2025-08-03 00:08:48,739 - root - DEBUG - Exiting application
2025-08-03 00:09:10,930 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:09:10,930 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:09:10,930 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:09:10,930 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:09:10,935 - config.settings - DEBUG -   mode: dev
2025-08-03 00:09:10,935 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:09:10,936 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:09:10,938 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:09:10,940 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:09:10,941 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:09:10,942 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:09:11,019 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:09:11,021 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:09:11,023 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:09:11,023 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:09:11,023 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:09:11,024 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:09:11,025 - root - DEBUG - Creating system tray icon
2025-08-03 00:09:11,052 - root - DEBUG - Tray icon dark
2025-08-03 00:09:11,057 - root - DEBUG - Tray icon displayed
2025-08-03 00:09:11,057 - root - DEBUG - Registering hotkey
2025-08-03 00:09:11,059 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:09:11,072 - root - DEBUG - Hotkey registered
2025-08-03 00:09:11,074 - root - DEBUG - Tray icon dark
2025-08-03 00:09:11,367 - config.settings - DEBUG - Saving settings:
2025-08-03 00:09:11,368 - config.settings - DEBUG -   mode: dev
2025-08-03 00:09:11,368 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:09:11,368 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:09:11,368 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:09:11,371 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:09:28,122 - root - DEBUG - triggered hotkey
2025-08-03 00:09:28,122 - root - DEBUG - Hotkey pressed
2025-08-03 00:09:28,122 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:09:28,124 - root - DEBUG - Showing popup window
2025-08-03 00:09:28,125 - root - DEBUG - Clipboard backup: "import logging

import markdown2
import pyperclip
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, Slot

from ui.UIUtils import colorMode

_ = lambda x: x

class NonEditableModal(QtWidgets.QDialog):
    """
    Modal window to display transformed text when pasting fails (non-editable page).
    Simple, clean interface that matches the app theme.
    """

    def __init__(self, app, transformed_text, original_text):
        super().__init__()
        self.app = app
        self.transformed_text = transformed_text
        self.original_text = original_text

        # No title, frameless window, always on top
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)

        self.setup_ui()
        self.apply_styles()
        self.position_near_cursor()
        
    def setup_ui(self):
        """Setup the user interface - clean and minimal"""
        # Main layout with minimal margins
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)

        # Container widget for content with padding
        container = QtWidgets.QWidget()
        container_layout = QtWidgets.QVBoxLayout(container)
        container_layout.setSpacing(12)
        container_layout.setContentsMargins(16, 16, 16, 16)

        # Text display area with markdown support
        self.text_display = QtWidgets.QTextBrowser()
        self.text_display.setReadOnly(True)
        self.text_display.setOpenExternalLinks(True)
        self.text_display.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.text_display.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Convert markdown to HTML
        html_content = markdown2.markdown(self.transformed_text, extras=['fenced-code-blocks', 'tables'])
        self.text_display.setHtml(html_content)

        # Set size for text display
        self.text_display.setMinimumHeight(300)
        self.text_display.setMinimumWidth(500)
        container_layout.addWidget(self.text_display)

        # Button container aligned to the right
        button_container = QtWidgets.QWidget()
        button_layout = QtWidgets.QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 8, 0, 0)
        button_layout.addStretch()  # Push buttons to the right

        # Copy button with custom icon
        self.copy_button = QtWidgets.QPushButton()
        self.copy_button.setFixedSize(36, 36)
        self.copy_button.clicked.connect(self.copy_text)
        self.copy_button.setDefault(True)
        self.copy_button.setToolTip(_("Copy text"))

        # Close button with X icon
        self.close_button = QtWidgets.QPushButton()
        self.close_button.setFixedSize(36, 36)
        self.close_button.clicked.connect(self.close)
        self.close_button.setToolTip(_("Close"))

        button_layout.addWidget(self.copy_button)
        button_layout.addSpacing(8)  # Small space between buttons
        button_layout.addWidget(self.close_button)
        container_layout.addWidget(button_container)

        layout.addWidget(container)

        # Set focus to copy button
        self.copy_button.setFocus()
        
    def apply_styles(self):
        """Apply dark/light mode styles matching the app theme"""
        is_dark_mode = colorMode == 'dark'

        if is_dark_mode:
            # Dark mode styles - matching the interface theme
            self.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #2a2a2a, stop:1 #1e1e1e);
                    border: 1px solid #404040;
                    border-radius: 12px;
                }
                QTextBrowser {
                    background-color: rgba(45, 45, 45, 0.9);
                    color: #ffffff;
                    border: 1px solid #404040;
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 14px;
                    line-height: 1.4;
                }
                QTextBrowser:focus {
                    border: 1px solid #0078d4;
                }
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555555;
                    border-radius: 8px;
                    color: #ffffff;
                }
                QPushButton:hover {
                    background-color: #4a9eff;
                    border-color: #4a9eff;
                }
                QPushButton:pressed {
                    background-color: #0066cc;
                }
            """)
        else:
            # Light mode styles - matching the interface theme
            self.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffffff, stop:1 #f5f5f5);
                    border: 1px solid #d0d0d0;
                    border-radius: 12px;
                }
                QTextBrowser {
                    background-color: rgba(255, 255, 255, 0.9);
                    color: #000000;
                    border: 1px solid #d0d0d0;
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 14px;
                    line-height: 1.4;
                }
                QTextBrowser:focus {
                    border: 1px solid #0078d4;
                }
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #d0d0d0;
                    border-radius: 8px;
                    color: #000000;
                }
                QPushButton:hover {
                    background-color: #4a9eff;
                    border-color: #4a9eff;
                    color: #ffffff;
                }
                QPushButton:pressed {
                    background-color: #0066cc;
                    color: #ffffff;
                }
            """)

        # Create icons for buttons
        self.create_copy_icon()
        self.create_close_icon()

    def create_copy_icon(self):
        """Create a custom copy icon with two overlapping rounded squares"""
        is_dark_mode = colorMode == 'dark'

        # Create pixmap for the icon
        pixmap = QtGui.QPixmap(24, 24)
        pixmap.fill(QtCore.Qt.GlobalColor.transparent)

        painter = QtGui.QPainter(pixmap)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Set colors based on theme
        if is_dark_mode:
            color = QtGui.QColor(255, 255, 255, 180)
        else:
            color = QtGui.QColor(0, 0, 0, 180)

        painter.setPen(QtGui.QPen(color, 1.5))
        painter.setBrush(QtCore.Qt.BrushStyle.NoBrush)

        # Draw back square (slightly offset)
        back_rect = QtCore.QRect(6, 6, 12, 12)
        painter.drawRoundedRect(back_rect, 2, 2)

        # Draw front square
        front_rect = QtCore.QRect(3, 3, 12, 12)
        painter.drawRoundedRect(front_rect, 2, 2)

        painter.end()

        # Set icon to button
        icon = QtGui.QIcon(pixmap)
        self.copy_button.setIcon(icon)

    def create_close_icon(self):
        """Create a custom close icon with X"""
        is_dark_mode = colorMode == 'dark'

        # Create pixmap for the icon
        pixmap = QtGui.QPixmap(24, 24)
        pixmap.fill(QtCore.Qt.GlobalColor.transparent)

        painter = QtGui.QPainter(pixmap)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Set colors based on theme
        if is_dark_mode:
            color = QtGui.QColor(255, 255, 255, 180)
        else:
            color = QtGui.QColor(0, 0, 0, 180)

        painter.setPen(QtGui.QPen(color, 2))

        # Draw X (two diagonal lines)
        painter.drawLine(8, 8, 16, 16)
        painter.drawLine(16, 8, 8, 16)

        painter.end()

        # Set icon to button
        icon = QtGui.QIcon(pixmap)
        self.close_button.setIcon(icon)

    def position_near_cursor(self):
        """Position the window near the cursor with larger size"""
        try:
            # Get cursor position
            cursor_pos = QtGui.QCursor.pos()

            # Get screen containing cursor
            screen = QtWidgets.QApplication.screenAt(cursor_pos)
            if screen is None:
                screen = QtWidgets.QApplication.primaryScreen()

            screen_geometry = screen.geometry()

            # Set larger window size for better text display
            self.resize(600, 450)

            # Calculate position (offset from cursor)
            x = cursor_pos.x() + 20
            y = cursor_pos.y() + 20

            # Adjust if window would go off screen
            if x + self.width() > screen_geometry.right():
                x = screen_geometry.right() - self.width()
            if y + self.height() > screen_geometry.bottom():
                y = cursor_pos.y() - self.height() - 20

            # Ensure window stays on screen
            x = max(screen_geometry.left(), x)
            y = max(screen_geometry.top(), y)

            self.move(x, y)

        except Exception as e:
            logging.error(f"Error positioning modal window: {e}")
            # Fallback to center of screen
            self.resize(600, 450)
            frame_geometry = self.frameGeometry()
            screen_center = QtWidgets.QApplication.primaryScreen().geometry().center()
            frame_geometry.moveCenter(screen_center)
            self.move(frame_geometry.topLeft())
    
    @Slot()
    def copy_text(self):
        """Copy the transformed text to clipboard"""
        try:
            pyperclip.copy(self.transformed_text)

            # Show brief visual feedback by changing button style
            is_dark_mode = colorMode == 'dark'

            if is_dark_mode:
                feedback_style = """
                    QPushButton {
                        background-color: #28a745;
                        border-color: #28a745;
                        color: #ffffff;
                    }
                """
            else:
                feedback_style = """
                    QPushButton {
                        background-color: #28a745;
                        border-color: #28a745;
                        color: #ffffff;
                    }
                """

            self.copy_button.setStyleSheet(feedback_style)
            self.copy_button.setEnabled(False)

            # Reset button after 1 second
            QtCore.QTimer.singleShot(1000, lambda: (
                self.copy_button.setStyleSheet(""),
                self.copy_button.setEnabled(True)
            ))

        except Exception as e:
            logging.error(f"Error copying text: {e}")
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            if event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.copy_text()
            else:
                self.close()
        else:
            super().keyPressEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()" (sleep: 0.2s)
2025-08-03 00:09:28,134 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:09:28,345 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:09:28,345 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth and power, has long been at the center of global financial narratives and conspiracy theories, true and not. Emerging from 18th-century Frankfurt, their banking empire shaped European and American economies, but a longer historical view reveals a millennia-hidden story, supported by modern DNA, that the Rothschilds are not Jewish but Babylonian Rādhānites (Turkic, Persian, Khazarian, Sogdian, Chinese)."
2025-08-03 00:09:28,350 - root - DEBUG - Creating new popup window
2025-08-03 00:09:28,350 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:09:28,350 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:09:28,386 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:09:28,387 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,389 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,390 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,393 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,394 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,394 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,397 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,398 - root - DEBUG - DraggableButton initialized
2025-08-03 00:09:28,402 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:09:28,406 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:09:28,406 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:09:28,484 - root - DEBUG - Popup window moved to position: (588, 390)
2025-08-03 00:09:42,280 - root - DEBUG - Processing option: Custom
2025-08-03 00:09:42,281 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:09:42,301 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:09:42,304 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:09:46,107 - root - DEBUG - No new text to process
2025-08-03 00:09:46,107 - root - DEBUG - Processing output text
2025-08-03 00:09:46,107 - root - DEBUG - Response processed
2025-08-03 00:09:46,109 - root - DEBUG - Clipboard backup: "import logging

import markdown2
import pyperclip
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, Slot

from ui.UIUtils import colorMode

_ = lambda x: x

class NonEditableModal(QtWidgets.QDialog):
    """
    Modal window to display transformed text when pasting fails (non-editable page).
    Simple, clean interface that matches the app theme.
    """

    def __init__(self, app, transformed_text, original_text):
        super().__init__()
        self.app = app
        self.transformed_text = transformed_text
        self.original_text = original_text

        # No title, frameless window, always on top
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)

        self.setup_ui()
        self.apply_styles()
        self.position_near_cursor()
        
    def setup_ui(self):
        """Setup the user interface - clean and minimal"""
        # Main layout with minimal margins
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)

        # Container widget for content with padding
        container = QtWidgets.QWidget()
        container_layout = QtWidgets.QVBoxLayout(container)
        container_layout.setSpacing(12)
        container_layout.setContentsMargins(16, 16, 16, 16)

        # Text display area with markdown support
        self.text_display = QtWidgets.QTextBrowser()
        self.text_display.setReadOnly(True)
        self.text_display.setOpenExternalLinks(True)
        self.text_display.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.text_display.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Convert markdown to HTML
        html_content = markdown2.markdown(self.transformed_text, extras=['fenced-code-blocks', 'tables'])
        self.text_display.setHtml(html_content)

        # Set size for text display
        self.text_display.setMinimumHeight(300)
        self.text_display.setMinimumWidth(500)
        container_layout.addWidget(self.text_display)

        # Button container aligned to the right
        button_container = QtWidgets.QWidget()
        button_layout = QtWidgets.QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 8, 0, 0)
        button_layout.addStretch()  # Push buttons to the right

        # Copy button with custom icon
        self.copy_button = QtWidgets.QPushButton()
        self.copy_button.setFixedSize(36, 36)
        self.copy_button.clicked.connect(self.copy_text)
        self.copy_button.setDefault(True)
        self.copy_button.setToolTip(_("Copy text"))

        # Close button with X icon
        self.close_button = QtWidgets.QPushButton()
        self.close_button.setFixedSize(36, 36)
        self.close_button.clicked.connect(self.close)
        self.close_button.setToolTip(_("Close"))

        button_layout.addWidget(self.copy_button)
        button_layout.addSpacing(8)  # Small space between buttons
        button_layout.addWidget(self.close_button)
        container_layout.addWidget(button_container)

        layout.addWidget(container)

        # Set focus to copy button
        self.copy_button.setFocus()
        
    def apply_styles(self):
        """Apply dark/light mode styles matching the app theme"""
        is_dark_mode = colorMode == 'dark'

        if is_dark_mode:
            # Dark mode styles - matching the interface theme
            self.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #2a2a2a, stop:1 #1e1e1e);
                    border: 1px solid #404040;
                    border-radius: 12px;
                }
                QTextBrowser {
                    background-color: rgba(45, 45, 45, 0.9);
                    color: #ffffff;
                    border: 1px solid #404040;
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 14px;
                    line-height: 1.4;
                }
                QTextBrowser:focus {
                    border: 1px solid #0078d4;
                }
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555555;
                    border-radius: 8px;
                    color: #ffffff;
                }
                QPushButton:hover {
                    background-color: #4a9eff;
                    border-color: #4a9eff;
                }
                QPushButton:pressed {
                    background-color: #0066cc;
                }
            """)
        else:
            # Light mode styles - matching the interface theme
            self.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffffff, stop:1 #f5f5f5);
                    border: 1px solid #d0d0d0;
                    border-radius: 12px;
                }
                QTextBrowser {
                    background-color: rgba(255, 255, 255, 0.9);
                    color: #000000;
                    border: 1px solid #d0d0d0;
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 14px;
                    line-height: 1.4;
                }
                QTextBrowser:focus {
                    border: 1px solid #0078d4;
                }
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #d0d0d0;
                    border-radius: 8px;
                    color: #000000;
                }
                QPushButton:hover {
                    background-color: #4a9eff;
                    border-color: #4a9eff;
                    color: #ffffff;
                }
                QPushButton:pressed {
                    background-color: #0066cc;
                    color: #ffffff;
                }
            """)

        # Create icons for buttons
        self.create_copy_icon()
        self.create_close_icon()

    def create_copy_icon(self):
        """Create a custom copy icon with two overlapping rounded squares"""
        is_dark_mode = colorMode == 'dark'

        # Create pixmap for the icon
        pixmap = QtGui.QPixmap(24, 24)
        pixmap.fill(QtCore.Qt.GlobalColor.transparent)

        painter = QtGui.QPainter(pixmap)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Set colors based on theme
        if is_dark_mode:
            color = QtGui.QColor(255, 255, 255, 180)
        else:
            color = QtGui.QColor(0, 0, 0, 180)

        painter.setPen(QtGui.QPen(color, 1.5))
        painter.setBrush(QtCore.Qt.BrushStyle.NoBrush)

        # Draw back square (slightly offset)
        back_rect = QtCore.QRect(6, 6, 12, 12)
        painter.drawRoundedRect(back_rect, 2, 2)

        # Draw front square
        front_rect = QtCore.QRect(3, 3, 12, 12)
        painter.drawRoundedRect(front_rect, 2, 2)

        painter.end()

        # Set icon to button
        icon = QtGui.QIcon(pixmap)
        self.copy_button.setIcon(icon)

    def create_close_icon(self):
        """Create a custom close icon with X"""
        is_dark_mode = colorMode == 'dark'

        # Create pixmap for the icon
        pixmap = QtGui.QPixmap(24, 24)
        pixmap.fill(QtCore.Qt.GlobalColor.transparent)

        painter = QtGui.QPainter(pixmap)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Set colors based on theme
        if is_dark_mode:
            color = QtGui.QColor(255, 255, 255, 180)
        else:
            color = QtGui.QColor(0, 0, 0, 180)

        painter.setPen(QtGui.QPen(color, 2))

        # Draw X (two diagonal lines)
        painter.drawLine(8, 8, 16, 16)
        painter.drawLine(16, 8, 8, 16)

        painter.end()

        # Set icon to button
        icon = QtGui.QIcon(pixmap)
        self.close_button.setIcon(icon)

    def position_near_cursor(self):
        """Position the window near the cursor with larger size"""
        try:
            # Get cursor position
            cursor_pos = QtGui.QCursor.pos()

            # Get screen containing cursor
            screen = QtWidgets.QApplication.screenAt(cursor_pos)
            if screen is None:
                screen = QtWidgets.QApplication.primaryScreen()

            screen_geometry = screen.geometry()

            # Set larger window size for better text display
            self.resize(600, 450)

            # Calculate position (offset from cursor)
            x = cursor_pos.x() + 20
            y = cursor_pos.y() + 20

            # Adjust if window would go off screen
            if x + self.width() > screen_geometry.right():
                x = screen_geometry.right() - self.width()
            if y + self.height() > screen_geometry.bottom():
                y = cursor_pos.y() - self.height() - 20

            # Ensure window stays on screen
            x = max(screen_geometry.left(), x)
            y = max(screen_geometry.top(), y)

            self.move(x, y)

        except Exception as e:
            logging.error(f"Error positioning modal window: {e}")
            # Fallback to center of screen
            self.resize(600, 450)
            frame_geometry = self.frameGeometry()
            screen_center = QtWidgets.QApplication.primaryScreen().geometry().center()
            frame_geometry.moveCenter(screen_center)
            self.move(frame_geometry.topLeft())
    
    @Slot()
    def copy_text(self):
        """Copy the transformed text to clipboard"""
        try:
            pyperclip.copy(self.transformed_text)

            # Show brief visual feedback by changing button style
            is_dark_mode = colorMode == 'dark'

            if is_dark_mode:
                feedback_style = """
                    QPushButton {
                        background-color: #28a745;
                        border-color: #28a745;
                        color: #ffffff;
                    }
                """
            else:
                feedback_style = """
                    QPushButton {
                        background-color: #28a745;
                        border-color: #28a745;
                        color: #ffffff;
                    }
                """

            self.copy_button.setStyleSheet(feedback_style)
            self.copy_button.setEnabled(False)

            # Reset button after 1 second
            QtCore.QTimer.singleShot(1000, lambda: (
                self.copy_button.setStyleSheet(""),
                self.copy_button.setEnabled(True)
            ))

        except Exception as e:
            logging.error(f"Error copying text: {e}")
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            if event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.copy_text()
            else:
                self.close()
        else:
            super().keyPressEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()" (sleep: 0.1s)
2025-08-03 00:09:46,112 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:09:46,226 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:09:46,437 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois)." (sleep: 0.1s)
2025-08-03 00:09:46,442 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:09:46,549 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:09:46,554 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:09:46,559 - root - DEBUG - Showing non-editable modal window
2025-08-03 00:10:35,708 - root - DEBUG - triggered hotkey
2025-08-03 00:10:35,708 - root - DEBUG - Hotkey pressed
2025-08-03 00:10:35,708 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:10:35,711 - root - DEBUG - Showing popup window
2025-08-03 00:10:35,712 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.2s)
2025-08-03 00:10:35,714 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:10:35,923 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:10:35,927 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth and power, has long been at the center of global financial narratives and conspiracy theories, true and not. Emerging from 18th-century Frankfurt, their banking empire shaped European and American economies, but a longer historical view reveals a millennia-hidden story, supported by modern DNA, that the Rothschilds are not Jewish but Babylonian Rādhānites (Turkic, Persian, Khazarian, Sogdian, Chinese)."
2025-08-03 00:10:35,927 - root - DEBUG - Existing popup window found
2025-08-03 00:10:35,927 - root - DEBUG - Creating new popup window
2025-08-03 00:10:35,927 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:10:35,931 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:10:35,939 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:10:35,940 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,942 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,944 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,945 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,946 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,947 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,948 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,949 - root - DEBUG - DraggableButton initialized
2025-08-03 00:10:35,952 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:10:35,953 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:10:35,954 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:10:35,983 - root - DEBUG - Popup window moved to position: (617, 288)
2025-08-03 00:10:40,127 - root - DEBUG - Processing option: Summary
2025-08-03 00:10:40,137 - root - DEBUG - Connecting response signals
2025-08-03 00:10:40,137 - root - DEBUG - Response signals connected
2025-08-03 00:10:40,168 - root - DEBUG - Starting processing thread for option: Summary
2025-08-03 00:10:40,173 - root - DEBUG - Getting response from provider for option: Summary
2025-08-03 00:10:40,200 - root - DEBUG - Getting response for window display
2025-08-03 00:10:42,875 - root - DEBUG - Got response of length: 540
2025-08-03 00:10:42,875 - root - DEBUG - Invoked set_text on response window
2025-08-03 00:11:15,748 - root - DEBUG - triggered hotkey
2025-08-03 00:11:15,749 - root - DEBUG - Hotkey pressed
2025-08-03 00:11:15,749 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:11:15,751 - root - DEBUG - Showing popup window
2025-08-03 00:11:15,751 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.2s)
2025-08-03 00:11:15,755 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:11:15,963 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:11:15,969 - root - DEBUG - Selected text: "The Rothschild family, a name synonymous with wealth"
2025-08-03 00:11:15,969 - root - DEBUG - Existing popup window found
2025-08-03 00:11:15,971 - root - DEBUG - Creating new popup window
2025-08-03 00:11:15,972 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:11:15,972 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:11:15,981 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:11:15,983 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,984 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,985 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,987 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,987 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,990 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,991 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,993 - root - DEBUG - DraggableButton initialized
2025-08-03 00:11:15,996 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:11:15,997 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:11:15,998 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:11:16,038 - root - DEBUG - Popup window moved to position: (1027, 236)
2025-08-03 00:11:21,472 - root - DEBUG - Processing option: Custom
2025-08-03 00:11:21,472 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:11:21,491 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:11:21,492 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:11:22,144 - root - DEBUG - No new text to process
2025-08-03 00:11:22,144 - root - DEBUG - Processing output text
2025-08-03 00:11:22,144 - root - DEBUG - Response processed
2025-08-03 00:11:22,144 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.1s)
2025-08-03 00:11:22,149 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:11:22,256 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:11:22,469 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse" (sleep: 0.1s)
2025-08-03 00:11:22,474 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:11:22,580 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:11:22,585 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:11:22,588 - root - DEBUG - Showing non-editable modal window
2025-08-03 00:12:07,060 - root - DEBUG - triggered hotkey
2025-08-03 00:12:07,061 - root - DEBUG - Hotkey pressed
2025-08-03 00:12:07,062 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:12:07,064 - root - DEBUG - Showing popup window
2025-08-03 00:12:07,064 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.2s)
2025-08-03 00:12:07,067 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:12:07,275 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:12:07,277 - root - DEBUG - Selected text: "The Babylonian Rādhānite people-group originated in Kish around 5300-4300 BC, initiating practices of usury (debt slavery), human slavery, and sacrifice of the innocents. They ran the banking, tax collection, government, commerce, public works, and law of Babylon/Baghdad.



They were identified as Rādhānites ca. 800 AD from their geographic location being a wealthy southeastern suburb of Baghdad named Rādhān between the Tigris and Euphrates Rivers. There they ran the Silk Road settlement banks—as they had done for millennia. Earlier they were identified in history from the extensive banking archives of the Egibi and Murašû families ca. 600 BC. who some historians even call “the Rothschilds of Babylon.” They have identified as “Jews” for four millennia, but were in fact an admixture of Silk Road people groups (i.e., Turkic, Khazarian, Persian, Sogdian, Chinese, even Indian).



These non-Jewish merchant-bankers were so deceptive that St. John the Revelator actually called them out in Revelation 2:9 (to the Church in Smyrna):  “I know about the slander of those who say they are Jews and are not, but are a synagogue of Satan.” St. John also called them out in Revelation 3:9 (to the Church in Philadelphia): “I will make those who are of the synagogue of Satan, who claim to be Jews though they are not, but are liars—I will make them’ come and fall down at your feet and acknowledge that I have loved you.”"
2025-08-03 00:12:07,277 - root - DEBUG - Existing popup window found
2025-08-03 00:12:07,277 - root - DEBUG - Creating new popup window
2025-08-03 00:12:07,282 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:12:07,283 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:12:07,290 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:12:07,291 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,293 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,293 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,297 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,298 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,298 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,301 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,305 - root - DEBUG - DraggableButton initialized
2025-08-03 00:12:07,308 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:12:07,313 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:12:07,313 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:12:07,347 - root - DEBUG - Popup window moved to position: (544, 189)
2025-08-03 00:12:10,767 - root - DEBUG - Processing option: Custom
2025-08-03 00:12:10,770 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:12:10,787 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:12:10,788 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:12:13,636 - root - DEBUG - No new text to process
2025-08-03 00:12:13,636 - root - DEBUG - Processing output text
2025-08-03 00:12:13,636 - root - DEBUG - Response processed
2025-08-03 00:12:13,636 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.1s)
2025-08-03 00:12:13,641 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:12:13,748 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:12:13,960 - root - DEBUG - Clipboard backup: "Le peuple-groupe babylonien des Rādhānites est originaire de Kish vers 5300-4300 av. J.-C., initiant des pratiques d'usure (esclavage pour dettes), d'esclavage humain et de sacrifice des innocents. Ils géraient la banque, la collecte des impôts, le gouvernement, le commerce, les travaux publics et le droit de Babylone/Bagdad.

Ils ont été identifiés comme Rādhānites vers 800 apr. J.-C. en raison de leur emplacement géographique, un riche faubourg sud-est de Bagdad nommé Rādhān, entre les fleuves Tigre et Euphrate. Là, ils géraient les banques de peuplement de la Route de la Soie, comme ils l'avaient fait pendant des millénaires. Auparavant, ils avaient été identifiés dans l'histoire à partir des vastes archives bancaires des familles Egibi et Murašû vers 600 av. J.-C., que certains historiens appellent même « les Rothschild de Babylone ». Ils se sont identifiés comme « Juifs » pendant quatre millénaires, mais étaient en fait un mélange de groupes de peuples de la Route de la Soie (c'est-à-dire turcs, khazars, perses, sogdiens, chinois, même indiens).

Ces marchands-banquiers non juifs étaient si trompeurs que saint Jean le Révélateur les a en fait dénoncés dans Apocalypse 2:9 (à l'Église de Smyrne) : « Je connais le blasphème de ceux qui se disent Juifs et ne le sont pas, mais sont une synagogue de Satan. » Saint Jean les a également dénoncés dans Apocalypse 3:9 (à l'Église de Philadelphie) : « Je ferai en sorte que ceux de la synagogue de Satan, qui se disent Juifs et ne le sont pas, mais sont des menteurs, je les ferai venir se prosterner à tes pieds et reconnaître que je t'ai aimé. »" (sleep: 0.1s)
2025-08-03 00:12:13,962 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:12:14,070 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:12:14,075 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:12:14,076 - root - DEBUG - Showing non-editable modal window
2025-08-03 00:15:20,228 - root - DEBUG - triggered hotkey
2025-08-03 00:15:20,229 - root - DEBUG - Hotkey pressed
2025-08-03 00:15:20,231 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:15:20,231 - root - DEBUG - Showing popup window
2025-08-03 00:15:20,232 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.2s)
2025-08-03 00:15:20,236 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:15:20,447 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:15:20,449 - root - DEBUG - Selected text: "is_dark_mode"
2025-08-03 00:15:20,449 - root - DEBUG - Existing popup window found
2025-08-03 00:15:20,449 - root - DEBUG - Creating new popup window
2025-08-03 00:15:20,449 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:15:20,455 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:15:20,462 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:15:20,465 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,468 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,470 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,471 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,473 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,475 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,477 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,481 - root - DEBUG - DraggableButton initialized
2025-08-03 00:15:20,483 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:15:20,485 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:15:20,486 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:15:20,528 - root - DEBUG - Popup window moved to position: (633, 390)
2025-08-03 00:15:23,594 - root - DEBUG - Processing option: Custom
2025-08-03 00:15:23,595 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:15:23,621 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:15:23,621 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:15:25,565 - root - DEBUG - No new text to process
2025-08-03 00:15:25,565 - root - DEBUG - Processing output text
2025-08-03 00:15:25,569 - root - DEBUG - Response processed
2025-08-03 00:15:25,569 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.1s)
2025-08-03 00:15:25,569 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:15:25,681 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:15:25,895 - root - DEBUG - Clipboard backup: "est_mode_sombre" (sleep: 0.1s)
2025-08-03 00:15:25,896 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:15:26,006 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:16:14,484 - root - DEBUG - triggered hotkey
2025-08-03 00:16:14,486 - root - DEBUG - Hotkey pressed
2025-08-03 00:16:14,486 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:16:14,488 - root - DEBUG - Showing popup window
2025-08-03 00:16:14,488 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.2s)
2025-08-03 00:16:14,493 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:16:14,701 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:16:14,702 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:16:14,706 - root - DEBUG - Existing popup window found
2025-08-03 00:16:14,708 - root - DEBUG - Creating new popup window
2025-08-03 00:16:14,709 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:16:14,709 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:16:14,716 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:16:14,717 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,719 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,721 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,723 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,723 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,723 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,728 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,729 - root - DEBUG - DraggableButton initialized
2025-08-03 00:16:14,734 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:16:14,734 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:16:14,736 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:16:14,771 - root - DEBUG - Popup window moved to position: (693, 80)
2025-08-03 00:16:22,930 - root - DEBUG - Processing option: Custom
2025-08-03 00:16:22,933 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:16:22,951 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:16:22,952 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:16:24,352 - root - DEBUG - No new text to process
2025-08-03 00:16:24,352 - root - DEBUG - Processing output text
2025-08-03 00:16:24,352 - root - DEBUG - Response processed
2025-08-03 00:16:24,353 - root - DEBUG - Clipboard backup: "La famille Rothschild, un nom synonyme de richesse et de pouvoir, est depuis longtemps au centre des récits financiers mondiaux et des théories du complot, fondées ou non. Émergeant de Francfort au XVIIIe siècle, leur empire bancaire a façonné les économies européennes et américaines, mais une perspective historique plus longue révèle une histoire cachée depuis des millénaires, étayée par l'ADN moderne, selon laquelle les Rothschild ne sont pas juifs mais des Rādhānites babyloniens (turcs, perses, khazars, sogdiens, chinois). " (sleep: 0.1s)
2025-08-03 00:16:24,357 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:16:24,466 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:16:24,679 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:16:24,679 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:16:24,790 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:16:24,794 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:16:24,798 - root - DEBUG - Showing non-editable modal window
2025-08-03 00:21:11,358 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:21:11,358 - root - DEBUG - Stopping the listener
2025-08-03 00:21:11,358 - root - DEBUG - Exiting application
2025-08-03 00:21:29,492 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:21:29,492 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:21:29,492 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:21:29,492 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:21:29,495 - config.settings - DEBUG -   mode: dev
2025-08-03 00:21:29,496 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:21:29,497 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:21:29,497 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:21:29,501 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:21:29,502 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:21:29,502 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:21:29,585 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:21:29,585 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:21:29,585 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:21:29,586 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:21:29,586 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:21:29,586 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:21:29,588 - root - DEBUG - Creating system tray icon
2025-08-03 00:21:29,616 - root - DEBUG - Tray icon dark
2025-08-03 00:21:29,621 - root - DEBUG - Tray icon displayed
2025-08-03 00:21:29,621 - root - DEBUG - Registering hotkey
2025-08-03 00:21:29,623 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:21:29,635 - root - DEBUG - Hotkey registered
2025-08-03 00:21:29,636 - root - DEBUG - Tray icon dark
2025-08-03 00:21:29,942 - config.settings - DEBUG - Saving settings:
2025-08-03 00:21:29,942 - config.settings - DEBUG -   mode: dev
2025-08-03 00:21:29,942 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:21:29,945 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:21:29,945 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:21:29,947 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:21:51,404 - root - DEBUG - triggered hotkey
2025-08-03 00:21:51,404 - root - DEBUG - Hotkey pressed
2025-08-03 00:21:51,404 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:21:51,408 - root - DEBUG - Showing popup window
2025-08-03 00:21:51,409 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:21:51,413 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:21:51,622 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:21:51,625 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:21:51,625 - root - DEBUG - Creating new popup window
2025-08-03 00:21:51,628 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:21:51,629 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:21:51,659 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:21:51,662 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,664 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,664 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,666 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,666 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,666 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,671 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,673 - root - DEBUG - DraggableButton initialized
2025-08-03 00:21:51,677 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:21:51,683 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:21:51,683 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:21:51,751 - root - DEBUG - Popup window moved to position: (823, 135)
2025-08-03 00:21:54,731 - root - DEBUG - Processing option: Custom
2025-08-03 00:21:54,731 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:21:54,750 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:21:54,750 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:21:55,977 - root - DEBUG - No new text to process
2025-08-03 00:21:55,977 - root - DEBUG - Processing output text
2025-08-03 00:21:55,977 - root - DEBUG - Response processed
2025-08-03 00:21:55,977 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:21:55,982 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:21:56,090 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:21:56,304 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:21:56,304 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:21:56,415 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:21:56,418 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:22:58,017 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:22:58,017 - root - DEBUG - Stopping the listener
2025-08-03 00:22:58,019 - root - DEBUG - Exiting application
2025-08-03 00:23:15,881 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:23:15,881 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:23:15,881 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:23:15,884 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:23:15,885 - config.settings - DEBUG -   mode: dev
2025-08-03 00:23:15,885 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:23:15,886 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:23:15,886 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:23:15,891 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:23:15,891 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:23:15,893 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:23:15,969 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:23:15,969 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:23:15,969 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:23:15,969 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:23:15,969 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:23:15,969 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:23:15,969 - root - DEBUG - Creating system tray icon
2025-08-03 00:23:16,002 - root - DEBUG - Tray icon dark
2025-08-03 00:23:16,006 - root - DEBUG - Tray icon displayed
2025-08-03 00:23:16,007 - root - DEBUG - Registering hotkey
2025-08-03 00:23:16,007 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:23:16,020 - root - DEBUG - Hotkey registered
2025-08-03 00:23:16,022 - root - DEBUG - Tray icon dark
2025-08-03 00:23:16,197 - config.settings - DEBUG - Saving settings:
2025-08-03 00:23:16,197 - config.settings - DEBUG -   mode: dev
2025-08-03 00:23:16,197 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:23:16,197 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:23:16,197 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:23:16,197 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:23:16,992 - root - DEBUG - triggered hotkey
2025-08-03 00:23:16,992 - root - DEBUG - Hotkey pressed
2025-08-03 00:23:16,992 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:23:16,994 - root - DEBUG - Showing popup window
2025-08-03 00:23:16,996 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:23:16,997 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:23:17,206 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:23:17,209 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:23:17,209 - root - DEBUG - Creating new popup window
2025-08-03 00:23:17,213 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:23:17,214 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:23:17,224 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:23:17,230 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,232 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,232 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,234 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,237 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,237 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,237 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,242 - root - DEBUG - DraggableButton initialized
2025-08-03 00:23:17,242 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:23:17,251 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:23:17,251 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:23:17,325 - root - DEBUG - Popup window moved to position: (562, 108)
2025-08-03 00:23:21,670 - root - DEBUG - Processing option: Custom
2025-08-03 00:23:21,670 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:23:21,693 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:23:21,693 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:23:23,280 - root - DEBUG - No new text to process
2025-08-03 00:23:23,280 - root - DEBUG - Processing output text
2025-08-03 00:23:23,280 - root - DEBUG - Response processed
2025-08-03 00:23:23,282 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:23:23,282 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:23:23,393 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:23:23,609 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:23:23,613 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:23:23,721 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:23:23,723 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:24:36,442 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:24:36,443 - root - DEBUG - Stopping the listener
2025-08-03 00:24:36,443 - root - DEBUG - Exiting application
2025-08-03 00:24:53,833 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:24:53,833 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:24:53,835 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:24:53,836 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:24:53,837 - config.settings - DEBUG -   mode: dev
2025-08-03 00:24:53,837 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:24:53,837 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:24:53,840 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:24:53,842 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:24:53,842 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:24:53,843 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:24:53,925 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:24:53,925 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:24:53,925 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:24:53,925 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:24:53,925 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:24:53,925 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:24:53,929 - root - DEBUG - Creating system tray icon
2025-08-03 00:24:53,958 - root - DEBUG - Tray icon dark
2025-08-03 00:24:53,964 - root - DEBUG - Tray icon displayed
2025-08-03 00:24:53,964 - root - DEBUG - Registering hotkey
2025-08-03 00:24:53,966 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:24:53,979 - root - DEBUG - Hotkey registered
2025-08-03 00:24:53,979 - root - DEBUG - Tray icon dark
2025-08-03 00:24:54,154 - config.settings - DEBUG - Saving settings:
2025-08-03 00:24:54,156 - config.settings - DEBUG -   mode: dev
2025-08-03 00:24:54,157 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:24:54,157 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:24:54,157 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:24:54,159 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:25:03,716 - root - DEBUG - triggered hotkey
2025-08-03 00:25:03,718 - root - DEBUG - Hotkey pressed
2025-08-03 00:25:03,719 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:25:03,719 - root - DEBUG - Showing popup window
2025-08-03 00:25:03,721 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:25:03,722 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:25:03,932 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:25:03,937 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:25:03,938 - root - DEBUG - Creating new popup window
2025-08-03 00:25:03,940 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:25:03,942 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:25:03,958 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:25:03,959 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,961 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,963 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,966 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,967 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,970 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,972 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,973 - root - DEBUG - DraggableButton initialized
2025-08-03 00:25:03,977 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:25:03,981 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:25:03,981 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:25:04,103 - root - DEBUG - Popup window moved to position: (552, 158)
2025-08-03 00:25:16,754 - root - DEBUG - Processing option: Custom
2025-08-03 00:25:16,758 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:25:16,777 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:25:16,779 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:25:18,549 - root - DEBUG - No new text to process
2025-08-03 00:25:18,549 - root - DEBUG - Processing output text
2025-08-03 00:25:18,554 - root - DEBUG - Response processed
2025-08-03 00:25:18,554 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:25:18,556 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:25:18,664 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:25:18,876 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:25:18,881 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:25:18,988 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:25:18,992 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:26:37,625 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:26:37,625 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:26:37,626 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:26:37,627 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:26:37,627 - config.settings - DEBUG -   mode: dev
2025-08-03 00:26:37,628 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:26:37,628 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:26:37,629 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:26:37,632 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:26:37,632 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:26:37,633 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:26:37,716 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:26:37,716 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:26:37,716 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:26:37,718 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:26:37,719 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:26:37,719 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:26:37,720 - root - DEBUG - Creating system tray icon
2025-08-03 00:26:37,746 - root - DEBUG - Tray icon dark
2025-08-03 00:26:37,754 - root - DEBUG - Tray icon displayed
2025-08-03 00:26:37,755 - root - DEBUG - Registering hotkey
2025-08-03 00:26:37,755 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:26:37,767 - root - DEBUG - Hotkey registered
2025-08-03 00:26:37,771 - root - DEBUG - Tray icon dark
2025-08-03 00:26:38,081 - config.settings - DEBUG - Saving settings:
2025-08-03 00:26:38,081 - config.settings - DEBUG -   mode: dev
2025-08-03 00:26:38,083 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:26:38,083 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:26:38,083 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:26:38,086 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:26:47,545 - root - DEBUG - triggered hotkey
2025-08-03 00:26:47,545 - root - DEBUG - Hotkey pressed
2025-08-03 00:26:47,545 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:26:47,547 - root - DEBUG - Showing popup window
2025-08-03 00:26:47,547 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:26:47,550 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:26:47,760 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:26:47,761 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:26:47,766 - root - DEBUG - Creating new popup window
2025-08-03 00:26:47,767 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:26:47,768 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:26:47,786 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:26:47,787 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,789 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,791 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,791 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,793 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,795 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,795 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,797 - root - DEBUG - DraggableButton initialized
2025-08-03 00:26:47,800 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:26:47,808 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:26:47,808 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:26:47,879 - root - DEBUG - Popup window moved to position: (644, 88)
2025-08-03 00:26:51,650 - root - DEBUG - Processing option: Custom
2025-08-03 00:26:51,651 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:26:51,667 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:26:51,668 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:26:53,238 - root - DEBUG - No new text to process
2025-08-03 00:26:53,238 - root - DEBUG - Processing output text
2025-08-03 00:26:53,238 - root - DEBUG - Response processed
2025-08-03 00:26:53,238 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:26:53,241 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:26:53,351 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:26:53,563 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes le long de la Route de la Soie jusqu'au port maritime de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:26:53,563 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:26:53,674 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:26:53,678 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 00:26:53,678 - root - DEBUG - Showing non-editable modal window
2025-08-03 00:28:53,551 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:28:53,551 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:28:53,551 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:28:53,551 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:28:53,553 - config.settings - DEBUG -   mode: dev
2025-08-03 00:28:53,555 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:28:53,556 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:28:53,556 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:28:53,559 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:28:53,560 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:28:53,561 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:28:53,636 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:28:53,636 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:28:53,636 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:28:53,636 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:28:53,636 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:28:53,638 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:28:53,639 - root - DEBUG - Creating system tray icon
2025-08-03 00:28:53,668 - root - DEBUG - Tray icon dark
2025-08-03 00:28:53,673 - root - DEBUG - Tray icon displayed
2025-08-03 00:28:53,673 - root - DEBUG - Registering hotkey
2025-08-03 00:28:53,674 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:28:53,684 - root - DEBUG - Hotkey registered
2025-08-03 00:28:53,688 - root - DEBUG - Tray icon dark
2025-08-03 00:28:53,868 - config.settings - DEBUG - Saving settings:
2025-08-03 00:28:53,870 - config.settings - DEBUG -   mode: dev
2025-08-03 00:28:53,870 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:28:53,872 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:28:53,872 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:28:53,872 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:28:57,782 - root - DEBUG - triggered hotkey
2025-08-03 00:28:57,786 - root - DEBUG - Hotkey pressed
2025-08-03 00:28:57,787 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:28:57,788 - root - DEBUG - Showing popup window
2025-08-03 00:28:57,790 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:28:57,794 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:28:57,803 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:28:57,803 - root - DEBUG - Stopping the listener
2025-08-03 00:28:57,803 - root - DEBUG - Exiting application
2025-08-03 00:28:58,002 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:28:58,003 - root - DEBUG - No text captured, retrying with longer sleep
2025-08-03 00:28:58,006 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.5s)
2025-08-03 00:28:58,010 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:28:58,019 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:28:58,019 - root - DEBUG - Stopping the listener
2025-08-03 00:28:58,021 - root - DEBUG - Exiting application
2025-08-03 00:28:58,516 - root - DEBUG - Waited 0.5s for clipboard
2025-08-03 00:28:58,520 - root - DEBUG - Selected text: ""
2025-08-03 00:28:58,520 - root - DEBUG - Creating new popup window
2025-08-03 00:28:58,523 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:28:58,523 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:28:58,542 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:28:58,547 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:28:58,548 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:28:58,617 - root - DEBUG - Popup window moved to position: (704, 470)
2025-08-03 00:29:19,958 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:29:19,958 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:29:19,958 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:29:19,958 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:29:19,961 - config.settings - DEBUG -   mode: dev
2025-08-03 00:29:19,961 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:29:19,962 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:29:19,963 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:29:19,965 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:29:19,967 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:29:19,968 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:29:20,052 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:29:20,056 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:29:20,056 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:29:20,057 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:29:20,057 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:29:20,057 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:29:20,059 - root - DEBUG - Creating system tray icon
2025-08-03 00:29:20,087 - root - DEBUG - Tray icon dark
2025-08-03 00:29:20,093 - root - DEBUG - Tray icon displayed
2025-08-03 00:29:20,093 - root - DEBUG - Registering hotkey
2025-08-03 00:29:20,094 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:29:20,105 - root - DEBUG - Hotkey registered
2025-08-03 00:29:20,108 - root - DEBUG - Tray icon dark
2025-08-03 00:29:20,288 - config.settings - DEBUG - Saving settings:
2025-08-03 00:29:20,288 - config.settings - DEBUG -   mode: dev
2025-08-03 00:29:20,288 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:29:20,288 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:29:20,291 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:29:20,291 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:29:22,553 - root - DEBUG - triggered hotkey
2025-08-03 00:29:22,554 - root - DEBUG - Hotkey pressed
2025-08-03 00:29:22,556 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:29:22,556 - root - DEBUG - Showing popup window
2025-08-03 00:29:22,558 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:29:22,558 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:29:22,769 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:29:22,775 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:29:22,775 - root - DEBUG - Creating new popup window
2025-08-03 00:29:22,777 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:29:22,778 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:29:22,791 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:29:22,792 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,793 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,795 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,797 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,798 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,800 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,800 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,802 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:22,806 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:29:22,811 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:29:22,813 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:29:22,887 - root - DEBUG - Popup window moved to position: (579, 401)
2025-08-03 00:29:33,618 - root - DEBUG - Processing option: Summary
2025-08-03 00:29:33,629 - root - DEBUG - Connecting response signals
2025-08-03 00:29:33,629 - root - DEBUG - Response signals connected
2025-08-03 00:29:33,654 - root - DEBUG - Starting processing thread for option: Summary
2025-08-03 00:29:33,661 - root - DEBUG - Getting response from provider for option: Summary
2025-08-03 00:29:33,661 - root - DEBUG - Getting response for window display
2025-08-03 00:29:34,814 - root - DEBUG - Got response of length: 288
2025-08-03 00:29:34,814 - root - DEBUG - Invoked set_text on response window
2025-08-03 00:29:40,652 - root - DEBUG - triggered hotkey
2025-08-03 00:29:40,653 - root - DEBUG - Hotkey pressed
2025-08-03 00:29:40,655 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:29:40,655 - root - DEBUG - Showing popup window
2025-08-03 00:29:40,655 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:29:40,660 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:29:40,868 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:29:40,873 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:29:40,873 - root - DEBUG - Existing popup window found
2025-08-03 00:29:40,874 - root - DEBUG - Creating new popup window
2025-08-03 00:29:40,875 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:29:40,876 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:29:40,885 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:29:40,886 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,887 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,889 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,891 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,892 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,894 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,897 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,897 - root - DEBUG - DraggableButton initialized
2025-08-03 00:29:40,899 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:29:40,903 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:29:40,903 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:29:40,936 - root - DEBUG - Popup window moved to position: (778, 105)
2025-08-03 00:30:04,776 - root - DEBUG - Processing option: Custom
2025-08-03 00:30:04,776 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:30:04,796 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:30:04,796 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:30:10,009 - root - DEBUG - No new text to process
2025-08-03 00:30:10,016 - root - DEBUG - Response processed
2025-08-03 00:30:24,986 - root - DEBUG - triggered hotkey
2025-08-03 00:30:24,987 - root - DEBUG - Hotkey pressed
2025-08-03 00:30:24,988 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:30:24,988 - root - DEBUG - Showing popup window
2025-08-03 00:30:24,988 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:30:24,991 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:30:25,199 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:30:25,203 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:30:25,203 - root - DEBUG - Existing popup window found
2025-08-03 00:30:25,205 - root - DEBUG - Creating new popup window
2025-08-03 00:30:25,208 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:30:25,209 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:30:25,215 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:30:25,215 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,218 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,220 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,221 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,224 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,226 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,227 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,227 - root - DEBUG - DraggableButton initialized
2025-08-03 00:30:25,232 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:30:25,234 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:30:25,234 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:30:25,272 - root - DEBUG - Popup window moved to position: (470, 349)
2025-08-03 00:31:39,870 - root - DEBUG - Processing option: Professional
2025-08-03 00:31:39,870 - root - DEBUG - Starting processing thread for option: Professional
2025-08-03 00:31:39,887 - root - DEBUG - Getting response from provider for option: Professional
2025-08-03 00:31:39,901 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:31:42,392 - root - DEBUG - triggered hotkey
2025-08-03 00:31:42,392 - root - DEBUG - Hotkey pressed
2025-08-03 00:31:42,394 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:31:42,395 - root - DEBUG - Showing popup window
2025-08-03 00:31:42,395 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 00:31:42,395 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:31:42,605 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:31:42,609 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:31:42,609 - root - DEBUG - Existing popup window found
2025-08-03 00:31:42,609 - root - DEBUG - Creating new popup window
2025-08-03 00:31:42,613 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:31:42,614 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:31:42,619 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:31:42,624 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,630 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,631 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,632 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,633 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,634 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,636 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,636 - root - DEBUG - DraggableButton initialized
2025-08-03 00:31:42,639 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:31:42,641 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:31:42,641 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:31:42,669 - root - DEBUG - Popup window moved to position: (594, 100)
2025-08-03 00:31:44,438 - root - DEBUG - No new text to process
2025-08-03 00:31:44,438 - root - DEBUG - Processing output text
2025-08-03 00:31:44,438 - root - DEBUG - Response processed
2025-08-03 00:31:44,439 - root - DEBUG - Clipboard backup: "Notamment, les villes qu'il mentionne sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:31:44,441 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:31:44,549 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:31:44,758 - root - DEBUG - Clipboard backup: "The cities referenced, Smyrna and Philadelphia, were strategically located along the Silk Road, which led to the seaport at Smyrna. From this port, goods were shipped to various destinations, including Italy, Spain, North Africa, France, Britain, and other Northern European countries, extending as far as Iceland." (sleep: 0.1s)
2025-08-03 00:31:44,762 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:31:44,867 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:32:12,237 - root - DEBUG - Processing option: Key Points
2025-08-03 00:32:12,249 - root - DEBUG - Connecting response signals
2025-08-03 00:32:12,250 - root - DEBUG - Response signals connected
2025-08-03 00:32:12,272 - root - DEBUG - Starting processing thread for option: Key Points
2025-08-03 00:32:12,277 - root - DEBUG - Getting response from provider for option: Key Points
2025-08-03 00:32:12,278 - root - DEBUG - Getting response for window display
2025-08-03 00:32:13,576 - root - DEBUG - Got response of length: 257
2025-08-03 00:32:13,579 - root - DEBUG - Invoked set_text on response window
2025-08-03 00:32:27,280 - root - DEBUG - Processing follow-up question: Écris un texte de 20 lignes.
2025-08-03 00:32:27,283 - root - DEBUG - Starting follow-up processing thread
2025-08-03 00:32:27,284 - root - DEBUG - Sending request to AI provider
2025-08-03 00:32:32,863 - root - DEBUG - Got response of length: 232
2025-08-03 00:32:50,011 - root - DEBUG - Processing follow-up question: Écris un texte de 15 lignes.
2025-08-03 00:32:50,012 - root - DEBUG - Starting follow-up processing thread
2025-08-03 00:32:50,012 - root - DEBUG - Sending request to AI provider
2025-08-03 00:32:53,987 - root - DEBUG - Got response of length: 836
2025-08-03 00:36:42,817 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:36:42,817 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:36:42,819 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:36:42,819 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:36:42,819 - config.settings - DEBUG -   mode: dev
2025-08-03 00:36:42,821 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:36:42,821 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:36:42,821 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:36:42,824 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:36:42,825 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:36:42,826 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:36:42,903 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:36:42,904 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:36:42,904 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:36:42,905 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:36:42,905 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:36:42,906 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:36:42,906 - root - DEBUG - Creating system tray icon
2025-08-03 00:36:42,939 - root - DEBUG - Tray icon dark
2025-08-03 00:36:42,943 - root - DEBUG - Tray icon displayed
2025-08-03 00:36:42,943 - root - DEBUG - Registering hotkey
2025-08-03 00:36:42,944 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:36:42,953 - root - DEBUG - Hotkey registered
2025-08-03 00:36:42,956 - root - DEBUG - Tray icon dark
2025-08-03 00:36:43,275 - config.settings - DEBUG - Saving settings:
2025-08-03 00:36:43,275 - config.settings - DEBUG -   mode: dev
2025-08-03 00:36:43,275 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:36:43,276 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:36:43,277 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:36:43,277 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:36:49,409 - root - DEBUG - triggered hotkey
2025-08-03 00:36:49,409 - root - DEBUG - Hotkey pressed
2025-08-03 00:36:49,412 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:36:49,413 - root - DEBUG - Showing popup window
2025-08-03 00:36:49,413 - root - DEBUG - Clipboard backup: "                    if (

                        len(self.current_response_window.chat_history) == 1

                    ):  # Only original text exists

                        self.current_response_window.chat_history.append(

                            {

                                "role": "assistant",

                                "content": self.output_queue.rstrip("\n"),

                            }

                        )" (sleep: 0.2s)
2025-08-03 00:36:49,417 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:36:49,626 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:36:49,627 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:36:49,627 - root - DEBUG - Creating new popup window
2025-08-03 00:36:49,627 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:36:49,632 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:36:49,661 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:36:49,663 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,664 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,664 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,666 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,667 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,667 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,667 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,670 - root - DEBUG - DraggableButton initialized
2025-08-03 00:36:49,674 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:36:49,678 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:36:49,678 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:36:49,759 - root - DEBUG - Popup window moved to position: (604, 137)
2025-08-03 00:37:10,279 - root - DEBUG - Processing option: Custom
2025-08-03 00:37:10,283 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:37:10,302 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:37:10,304 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:37:15,839 - root - DEBUG - No new text to process
2025-08-03 00:37:15,839 - root - DEBUG - Processing output text
2025-08-03 00:37:15,839 - root - DEBUG - Response processed
2025-08-03 00:37:15,840 - root - DEBUG - Clipboard backup: "                    if (

                        len(self.current_response_window.chat_history) == 1

                    ):  # Only original text exists

                        self.current_response_window.chat_history.append(

                            {

                                "role": "assistant",

                                "content": self.output_queue.rstrip("\n"),

                            }

                        )" (sleep: 0.1s)
2025-08-03 00:37:15,841 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:37:15,954 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:37:16,165 - root - DEBUG - Clipboard backup: "Italie, Espagne, Afrique du Nord, France, Grande-Bretagne, Islande" (sleep: 0.1s)
2025-08-03 00:37:16,165 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:37:16,275 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:37:16,278 - root - ERROR - Error processing output: 'WritingToolApp' object has no attribute 'current_response_window'
2025-08-03 00:50:16,220 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:50:16,220 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:50:16,220 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:50:16,220 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:50:16,220 - config.settings - DEBUG -   mode: dev
2025-08-03 00:50:16,220 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:50:16,225 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:50:16,225 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:50:16,225 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:50:16,225 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:50:16,230 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:50:16,299 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:50:16,301 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:50:16,301 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:50:16,301 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:50:16,301 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:50:16,304 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:50:16,304 - root - DEBUG - Creating system tray icon
2025-08-03 00:50:16,329 - root - DEBUG - Tray icon dark
2025-08-03 00:50:16,332 - root - DEBUG - Tray icon displayed
2025-08-03 00:50:16,332 - root - DEBUG - Registering hotkey
2025-08-03 00:50:16,332 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:50:16,343 - root - DEBUG - Hotkey registered
2025-08-03 00:50:16,349 - root - DEBUG - Tray icon dark
2025-08-03 00:50:16,644 - config.settings - DEBUG - Saving settings:
2025-08-03 00:50:16,644 - config.settings - DEBUG -   mode: dev
2025-08-03 00:50:16,647 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:50:16,647 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:50:16,647 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:50:16,647 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:50:28,142 - root - DEBUG - triggered hotkey
2025-08-03 00:50:28,142 - root - DEBUG - Hotkey pressed
2025-08-03 00:50:28,147 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:50:28,149 - root - DEBUG - Showing popup window
2025-08-03 00:50:28,149 - root - DEBUG - Clipboard backup: "                if hasattr(self, "current_response_window"):

                    self.current_response_window.append_text(new_text)" (sleep: 0.2s)
2025-08-03 00:50:28,155 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:50:28,362 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:50:28,362 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:50:28,367 - root - DEBUG - Creating new popup window
2025-08-03 00:50:28,367 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:50:28,368 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:50:28,385 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:50:28,386 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,388 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,388 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,390 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,392 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,392 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,392 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,396 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:28,399 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:50:28,405 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:50:28,407 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:50:28,483 - root - DEBUG - Popup window moved to position: (727, 121)
2025-08-03 00:50:33,919 - root - DEBUG - Processing option: Summary
2025-08-03 00:50:33,931 - root - DEBUG - Connecting response signals
2025-08-03 00:50:33,934 - root - DEBUG - Response signals connected
2025-08-03 00:50:33,957 - root - DEBUG - Starting processing thread for option: Summary
2025-08-03 00:50:33,963 - root - DEBUG - Getting response from provider for option: Summary
2025-08-03 00:50:33,963 - root - DEBUG - Getting response for window display
2025-08-03 00:50:36,899 - root - DEBUG - Got response of length: 319
2025-08-03 00:50:36,899 - root - DEBUG - Invoked set_text on response window
2025-08-03 00:50:41,831 - root - DEBUG - triggered hotkey
2025-08-03 00:50:41,832 - root - DEBUG - Hotkey pressed
2025-08-03 00:50:41,832 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:50:41,834 - root - DEBUG - Showing popup window
2025-08-03 00:50:41,835 - root - DEBUG - Clipboard backup: "                if hasattr(self, "current_response_window"):

                    self.current_response_window.append_text(new_text)" (sleep: 0.2s)
2025-08-03 00:50:41,837 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:50:42,046 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:50:42,049 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:50:42,049 - root - DEBUG - Existing popup window found
2025-08-03 00:50:42,051 - root - DEBUG - Creating new popup window
2025-08-03 00:50:42,052 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:50:42,053 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:50:42,060 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:50:42,061 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,064 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,066 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,067 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,069 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,070 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,072 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,072 - root - DEBUG - DraggableButton initialized
2025-08-03 00:50:42,077 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:50:42,079 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:50:42,079 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:50:42,108 - root - DEBUG - Popup window moved to position: (737, 115)
2025-08-03 00:50:46,167 - root - DEBUG - Processing option: Custom
2025-08-03 00:50:46,167 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:50:46,187 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:50:46,188 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:50:47,609 - root - DEBUG - No new text to process
2025-08-03 00:50:47,609 - root - DEBUG - Processing output text
2025-08-03 00:50:47,609 - root - DEBUG - Response processed
2025-08-03 00:50:47,614 - root - DEBUG - Clipboard backup: "                if hasattr(self, "current_response_window"):

                    self.current_response_window.append_text(new_text)" (sleep: 0.1s)
2025-08-03 00:50:47,617 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:50:47,726 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:50:47,939 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne, et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:50:47,941 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:50:48,050 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:52:08,137 - root - INFO - Received SIGINT. Exiting...
2025-08-03 00:52:08,137 - root - DEBUG - Stopping the listener
2025-08-03 00:52:08,139 - root - DEBUG - Exiting application
2025-08-03 00:52:23,266 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 00:52:23,266 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 00:52:23,266 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 00:52:23,269 - config.settings - DEBUG -   base_dir: .
2025-08-03 00:52:23,269 - config.settings - DEBUG -   mode: dev
2025-08-03 00:52:23,270 - config.settings - DEBUG -   config_dir: config
2025-08-03 00:52:23,271 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 00:52:23,272 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:52:23,276 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 00:52:23,276 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 00:52:23,277 - root - DEBUG - Unified settings loaded successfully
2025-08-03 00:52:23,363 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 00:52:23,364 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 00:52:23,364 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 00:52:23,364 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 00:52:23,364 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 00:52:23,367 - root - DEBUG - Provider config loaded successfully
2025-08-03 00:52:23,367 - root - DEBUG - Creating system tray icon
2025-08-03 00:52:23,414 - root - DEBUG - Tray icon dark
2025-08-03 00:52:23,420 - root - DEBUG - Tray icon displayed
2025-08-03 00:52:23,421 - root - DEBUG - Registering hotkey
2025-08-03 00:52:23,422 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 00:52:23,446 - root - DEBUG - Hotkey registered
2025-08-03 00:52:23,450 - root - DEBUG - Tray icon dark
2025-08-03 00:52:23,663 - config.settings - DEBUG - Saving settings:
2025-08-03 00:52:23,665 - config.settings - DEBUG -   mode: dev
2025-08-03 00:52:23,667 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 00:52:23,667 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 00:52:23,669 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 00:52:23,670 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 00:52:26,160 - root - DEBUG - triggered hotkey
2025-08-03 00:52:26,162 - root - DEBUG - Hotkey pressed
2025-08-03 00:52:26,162 - root - DEBUG - Cancelling current provider's request
2025-08-03 00:52:26,164 - root - DEBUG - Showing popup window
2025-08-03 00:52:26,167 - root - DEBUG - Clipboard backup: "                if hasattr(self, "current_response_window"):

                    self.current_response_window.append_text(new_text)" (sleep: 0.2s)
2025-08-03 00:52:26,171 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:52:26,377 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 00:52:26,382 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 00:52:26,382 - root - DEBUG - Creating new popup window
2025-08-03 00:52:26,382 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 00:52:26,385 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 00:52:26,418 - root - DEBUG - Loading actions from unified settings
2025-08-03 00:52:26,419 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,420 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,420 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,420 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,426 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,428 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,430 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,432 - root - DEBUG - DraggableButton initialized
2025-08-03 00:52:26,435 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 00:52:26,441 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 00:52:26,442 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 00:52:26,518 - root - DEBUG - Popup window moved to position: (688, 82)
2025-08-03 00:52:59,976 - root - DEBUG - Processing option: Custom
2025-08-03 00:52:59,984 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 00:53:00,001 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 00:53:00,003 - root - DEBUG - Getting response for direct replacement
2025-08-03 00:53:01,741 - root - DEBUG - No new text to process
2025-08-03 00:53:01,741 - root - DEBUG - Processing output text
2025-08-03 00:53:01,743 - root - DEBUG - Response processed
2025-08-03 00:53:01,743 - root - DEBUG - Clipboard backup: "                if hasattr(self, "current_response_window"):

                    self.current_response_window.append_text(new_text)" (sleep: 0.1s)
2025-08-03 00:53:01,743 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:53:01,854 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:53:02,067 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 00:53:02,071 - root - DEBUG - Simulating Ctrl+C
2025-08-03 00:53:02,179 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 00:53:02,183 - root - ERROR - Error processing output: 'WritingToolApp' object has no attribute 'current_response_window'
2025-08-03 01:02:34,377 - root - INFO - Received SIGINT. Exiting...
2025-08-03 01:02:34,377 - root - DEBUG - Stopping the listener
2025-08-03 01:02:34,377 - root - DEBUG - Exiting application
2025-08-03 01:02:46,671 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 01:02:46,673 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 01:02:46,673 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 01:02:46,673 - config.settings - DEBUG -   base_dir: .
2025-08-03 01:02:46,675 - config.settings - DEBUG -   mode: dev
2025-08-03 01:02:46,675 - config.settings - DEBUG -   config_dir: config
2025-08-03 01:02:46,676 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 01:02:46,676 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 01:02:46,679 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 01:02:46,680 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 01:02:46,681 - root - DEBUG - Unified settings loaded successfully
2025-08-03 01:02:46,761 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 01:02:46,761 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 01:02:46,761 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 01:02:46,764 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 01:02:46,764 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 01:02:46,764 - root - DEBUG - Provider config loaded successfully
2025-08-03 01:02:46,765 - root - DEBUG - Creating system tray icon
2025-08-03 01:02:46,796 - root - DEBUG - Tray icon dark
2025-08-03 01:02:46,801 - root - DEBUG - Tray icon displayed
2025-08-03 01:02:46,802 - root - DEBUG - Registering hotkey
2025-08-03 01:02:46,803 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 01:02:46,816 - root - DEBUG - Hotkey registered
2025-08-03 01:02:46,820 - root - DEBUG - Tray icon dark
2025-08-03 01:02:47,146 - config.settings - DEBUG - Saving settings:
2025-08-03 01:02:47,147 - config.settings - DEBUG -   mode: dev
2025-08-03 01:02:47,147 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 01:02:47,147 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 01:02:47,147 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 01:02:47,150 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 01:02:49,357 - root - DEBUG - triggered hotkey
2025-08-03 01:02:49,357 - root - DEBUG - Hotkey pressed
2025-08-03 01:02:49,357 - root - DEBUG - Cancelling current provider's request
2025-08-03 01:02:49,359 - root - DEBUG - Showing popup window
2025-08-03 01:02:49,360 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 01:02:49,365 - root - DEBUG - Simulating Ctrl+C
2025-08-03 01:02:49,573 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 01:02:49,576 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 01:02:49,576 - root - DEBUG - Creating new popup window
2025-08-03 01:02:49,581 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 01:02:49,582 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 01:02:49,597 - root - DEBUG - Loading actions from unified settings
2025-08-03 01:02:49,599 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,600 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,602 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,603 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,605 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,605 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,605 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,609 - root - DEBUG - DraggableButton initialized
2025-08-03 01:02:49,613 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 01:02:49,620 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 01:02:49,652 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 01:02:49,755 - root - DEBUG - Popup window moved to position: (629, 93)
2025-08-03 01:02:53,642 - root - DEBUG - Processing option: Custom
2025-08-03 01:02:53,643 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 01:02:53,662 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 01:02:53,662 - root - DEBUG - Getting response for direct replacement
2025-08-03 01:02:55,444 - root - DEBUG - No new text to process
2025-08-03 01:02:55,444 - root - DEBUG - Processing output text
2025-08-03 01:02:55,444 - root - DEBUG - Response processed
2025-08-03 01:02:55,444 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 01:02:55,446 - root - DEBUG - Simulating Ctrl+C
2025-08-03 01:02:55,554 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 01:02:55,767 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 01:02:55,770 - root - DEBUG - Simulating Ctrl+C
2025-08-03 01:02:55,879 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 01:02:55,884 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 01:02:55,888 - root - DEBUG - Showing non-editable modal window
2025-08-03 01:04:29,641 - root - INFO - Received SIGINT. Exiting...
2025-08-03 01:04:29,641 - root - DEBUG - Stopping the listener
2025-08-03 01:04:29,643 - root - DEBUG - Exiting application
2025-08-03 01:04:44,112 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 01:04:44,112 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 01:04:44,112 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 01:04:44,114 - config.settings - DEBUG -   base_dir: .
2025-08-03 01:04:44,114 - config.settings - DEBUG -   mode: dev
2025-08-03 01:04:44,115 - config.settings - DEBUG -   config_dir: config
2025-08-03 01:04:44,116 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 01:04:44,116 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 01:04:44,119 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 01:04:44,121 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 01:04:44,122 - root - DEBUG - Unified settings loaded successfully
2025-08-03 01:04:44,249 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 01:04:44,249 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 01:04:44,251 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 01:04:44,252 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 01:04:44,252 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 01:04:44,253 - root - DEBUG - Provider config loaded successfully
2025-08-03 01:04:44,254 - root - DEBUG - Creating system tray icon
2025-08-03 01:04:44,277 - root - DEBUG - Tray icon dark
2025-08-03 01:04:44,282 - root - DEBUG - Tray icon displayed
2025-08-03 01:04:44,283 - root - DEBUG - Registering hotkey
2025-08-03 01:04:44,284 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 01:04:44,296 - root - DEBUG - Hotkey registered
2025-08-03 01:04:44,296 - root - DEBUG - Tray icon dark
2025-08-03 01:04:44,474 - config.settings - DEBUG - Saving settings:
2025-08-03 01:04:44,476 - config.settings - DEBUG -   mode: dev
2025-08-03 01:04:44,476 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 01:04:44,476 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 01:04:44,476 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 01:04:44,476 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 01:04:44,931 - root - DEBUG - triggered hotkey
2025-08-03 01:04:44,931 - root - DEBUG - Hotkey pressed
2025-08-03 01:04:44,931 - root - DEBUG - Cancelling current provider's request
2025-08-03 01:04:44,933 - root - DEBUG - Showing popup window
2025-08-03 01:04:44,933 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.2s)
2025-08-03 01:04:44,939 - root - DEBUG - Simulating Ctrl+C
2025-08-03 01:04:45,147 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 01:04:45,151 - root - DEBUG - Selected text: "Notably, the cities he references are Smyrna and Philadelphia two cities along the Silk Road to the seaport at Smyrna where goods were onboarded and taken to Italy, Spain, North Africa, France, Britain, and the rest of Northern Europe, even to Iceland."
2025-08-03 01:04:45,153 - root - DEBUG - Creating new popup window
2025-08-03 01:04:45,154 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 01:04:45,155 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 01:04:45,171 - root - DEBUG - Loading actions from unified settings
2025-08-03 01:04:45,173 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,174 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,176 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,178 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,180 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,182 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,184 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,185 - root - DEBUG - DraggableButton initialized
2025-08-03 01:04:45,189 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 01:04:45,198 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 01:04:45,199 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 01:04:45,278 - root - DEBUG - Popup window moved to position: (725, 82)
2025-08-03 01:04:48,925 - root - DEBUG - Processing option: Custom
2025-08-03 01:04:48,925 - root - DEBUG - Starting processing thread for option: Custom
2025-08-03 01:04:48,943 - root - DEBUG - Getting response from provider for option: Custom
2025-08-03 01:04:48,946 - root - DEBUG - Getting response for direct replacement
2025-08-03 01:04:49,875 - root - DEBUG - No new text to process
2025-08-03 01:04:49,875 - root - DEBUG - Processing output text
2025-08-03 01:04:49,875 - root - DEBUG - Response processed
2025-08-03 01:04:49,878 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et acheminées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 01:04:49,878 - root - DEBUG - Simulating Ctrl+C
2025-08-03 01:04:49,990 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 01:04:50,203 - root - DEBUG - Clipboard backup: "Notamment, les villes auxquelles il fait référence sont Smyrne et Philadelphie, deux villes situées le long de la Route de la Soie jusqu'au port de Smyrne où les marchandises étaient embarquées et transportées vers l'Italie, l'Espagne, l'Afrique du Nord, la France, la Grande-Bretagne et le reste de l'Europe du Nord, même jusqu'en Islande." (sleep: 0.1s)
2025-08-03 01:04:50,205 - root - DEBUG - Simulating Ctrl+C
2025-08-03 01:04:50,315 - root - DEBUG - Waited 0.1s for clipboard
2025-08-03 01:04:50,318 - root - DEBUG - Paste failed - showing modal window for non-editable page
2025-08-03 01:04:50,321 - root - DEBUG - Showing non-editable modal window
2025-08-03 01:09:39,679 - root - INFO - Received SIGINT. Exiting...
2025-08-03 01:09:39,679 - root - DEBUG - Stopping the listener
2025-08-03 01:09:39,679 - root - DEBUG - Exiting application
2025-08-03 15:28:57,896 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 15:28:57,898 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 15:28:57,900 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 15:28:57,901 - config.settings - DEBUG -   base_dir: .
2025-08-03 15:28:57,901 - config.settings - DEBUG -   mode: dev
2025-08-03 15:28:57,902 - config.settings - DEBUG -   config_dir: config
2025-08-03 15:28:57,903 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 15:28:57,905 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 15:28:57,922 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 15:28:57,922 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 15:28:57,923 - root - DEBUG - Unified settings loaded successfully
2025-08-03 15:28:58,226 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 15:28:58,227 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 15:28:58,227 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 15:28:58,228 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 15:28:58,228 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 15:28:58,230 - root - DEBUG - Provider config loaded successfully
2025-08-03 15:28:58,231 - root - DEBUG - Creating system tray icon
2025-08-03 15:28:58,298 - root - DEBUG - Tray icon dark
2025-08-03 15:28:58,305 - root - DEBUG - Tray icon displayed
2025-08-03 15:28:58,306 - root - DEBUG - Registering hotkey
2025-08-03 15:28:58,307 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 15:28:58,324 - root - DEBUG - Hotkey registered
2025-08-03 15:28:58,366 - root - DEBUG - Tray icon dark
2025-08-03 15:28:58,693 - config.settings - DEBUG - Saving settings:
2025-08-03 15:28:58,694 - config.settings - DEBUG -   mode: dev
2025-08-03 15:28:58,695 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 15:28:58,695 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 15:28:58,696 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 15:28:58,699 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 15:59:12,726 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 15:59:12,728 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 15:59:12,730 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 15:59:12,732 - config.settings - DEBUG -   base_dir: .
2025-08-03 15:59:12,733 - config.settings - DEBUG -   mode: dev
2025-08-03 15:59:12,735 - config.settings - DEBUG -   config_dir: config
2025-08-03 15:59:12,737 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 15:59:12,738 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 15:59:13,093 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 15:59:13,094 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 15:59:13,096 - root - DEBUG - Unified settings loaded successfully
2025-08-03 15:59:13,686 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 15:59:13,687 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 15:59:13,688 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 15:59:19,973 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 15:59:36,136 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 15:59:57,484 - root - DEBUG - Provider config loaded successfully
2025-08-03 16:00:03,406 - root - DEBUG - Creating system tray icon
2025-08-03 16:01:35,060 - root - DEBUG - Tray icon dark
2025-08-03 16:01:35,076 - root - DEBUG - Tray icon displayed
2025-08-03 16:01:35,077 - root - DEBUG - Registering hotkey
2025-08-03 16:01:35,078 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 16:01:35,094 - root - DEBUG - Hotkey registered
2025-08-03 16:01:35,100 - root - DEBUG - Tray icon dark
2025-08-03 16:01:35,438 - config.settings - DEBUG - Saving settings:
2025-08-03 16:01:35,439 - config.settings - DEBUG -   mode: dev
2025-08-03 16:01:35,441 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:01:35,441 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 16:01:35,442 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 16:01:35,445 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 16:03:26,567 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 16:03:26,568 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 16:03:26,568 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 16:03:26,569 - config.settings - DEBUG -   base_dir: .
2025-08-03 16:03:26,569 - config.settings - DEBUG -   mode: dev
2025-08-03 16:03:26,570 - config.settings - DEBUG -   config_dir: config
2025-08-03 16:03:26,570 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 16:03:26,572 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:03:26,575 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 16:03:26,578 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 16:03:26,579 - root - DEBUG - Unified settings loaded successfully
2025-08-03 16:03:26,664 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 16:03:26,665 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 16:03:26,666 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 16:03:26,667 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 16:03:26,667 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 16:03:26,668 - root - DEBUG - Provider config loaded successfully
2025-08-03 16:03:26,669 - root - DEBUG - Creating system tray icon
2025-08-03 16:03:26,699 - root - DEBUG - Tray icon dark
2025-08-03 16:03:26,704 - root - DEBUG - Tray icon displayed
2025-08-03 16:03:26,705 - root - DEBUG - Registering hotkey
2025-08-03 16:03:26,706 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 16:03:26,720 - root - DEBUG - Hotkey registered
2025-08-03 16:03:26,724 - root - DEBUG - Tray icon dark
2025-08-03 16:03:26,918 - config.settings - DEBUG - Saving settings:
2025-08-03 16:03:26,918 - config.settings - DEBUG -   mode: dev
2025-08-03 16:03:26,920 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:03:26,920 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 16:03:26,921 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 16:03:26,923 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 16:19:18,308 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 16:19:18,309 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 16:19:18,310 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 16:19:18,311 - config.settings - DEBUG -   base_dir: .
2025-08-03 16:19:18,312 - config.settings - DEBUG -   mode: dev
2025-08-03 16:19:18,312 - config.settings - DEBUG -   config_dir: config
2025-08-03 16:19:18,314 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 16:19:18,314 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:19:18,319 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 16:19:18,320 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 16:19:18,321 - root - DEBUG - Unified settings loaded successfully
2025-08-03 16:19:18,407 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 16:19:18,408 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 16:19:18,410 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 16:19:18,412 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 16:19:18,412 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 16:19:18,413 - root - DEBUG - Provider config loaded successfully
2025-08-03 16:19:18,414 - root - DEBUG - Creating system tray icon
2025-08-03 16:19:18,446 - root - DEBUG - Tray icon dark
2025-08-03 16:19:18,450 - root - DEBUG - Tray icon displayed
2025-08-03 16:19:18,450 - root - DEBUG - Registering hotkey
2025-08-03 16:19:18,451 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 16:19:18,466 - root - DEBUG - Hotkey registered
2025-08-03 16:19:18,470 - root - DEBUG - Tray icon dark
2025-08-03 16:19:18,796 - config.settings - DEBUG - Saving settings:
2025-08-03 16:19:18,797 - config.settings - DEBUG -   mode: dev
2025-08-03 16:19:18,798 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:19:18,798 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 16:19:18,799 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 16:19:18,802 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 16:21:03,066 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 16:21:03,067 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 16:21:03,068 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 16:21:03,069 - config.settings - DEBUG -   base_dir: .
2025-08-03 16:21:03,069 - config.settings - DEBUG -   mode: dev
2025-08-03 16:21:03,071 - config.settings - DEBUG -   config_dir: config
2025-08-03 16:21:03,072 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 16:21:03,072 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:21:03,076 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 16:21:03,078 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 16:21:03,079 - root - DEBUG - Unified settings loaded successfully
2025-08-03 16:21:03,162 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 16:21:03,162 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 16:21:03,163 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 16:21:03,164 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 16:21:03,165 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 16:21:03,167 - root - DEBUG - Provider config loaded successfully
2025-08-03 16:21:03,168 - root - DEBUG - Creating system tray icon
2025-08-03 16:21:03,199 - root - DEBUG - Tray icon dark
2025-08-03 16:21:03,205 - root - DEBUG - Tray icon displayed
2025-08-03 16:21:03,206 - root - DEBUG - Registering hotkey
2025-08-03 16:21:03,208 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 16:21:03,224 - root - DEBUG - Hotkey registered
2025-08-03 16:21:03,228 - root - DEBUG - Tray icon dark
2025-08-03 16:21:03,415 - config.settings - DEBUG - Saving settings:
2025-08-03 16:21:03,416 - config.settings - DEBUG -   mode: dev
2025-08-03 16:21:03,416 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:21:03,418 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 16:21:03,419 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 16:21:03,422 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 16:21:15,207 - root - DEBUG - Stopping the listener
2025-08-03 16:21:15,209 - root - DEBUG - Exiting application
2025-08-03 16:26:16,865 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-03 16:26:16,866 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-03 16:26:16,868 - config.settings - DEBUG - SettingsManager initialized:
2025-08-03 16:26:16,869 - config.settings - DEBUG -   base_dir: .
2025-08-03 16:26:16,870 - config.settings - DEBUG -   mode: dev
2025-08-03 16:26:16,871 - config.settings - DEBUG -   config_dir: config
2025-08-03 16:26:16,872 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-03 16:26:16,873 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:26:16,879 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-03 16:26:16,880 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-03 16:26:16,882 - root - DEBUG - Unified settings loaded successfully
2025-08-03 16:26:16,963 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-03 16:26:16,963 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-03 16:26:16,965 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-03 16:26:16,966 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-03 16:26:16,967 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-03 16:26:16,968 - root - DEBUG - Provider config loaded successfully
2025-08-03 16:26:16,969 - root - DEBUG - Creating system tray icon
2025-08-03 16:26:16,999 - root - DEBUG - Tray icon dark
2025-08-03 16:26:17,004 - root - DEBUG - Tray icon displayed
2025-08-03 16:26:17,006 - root - DEBUG - Registering hotkey
2025-08-03 16:26:17,007 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-03 16:26:17,020 - root - DEBUG - Hotkey registered
2025-08-03 16:26:17,025 - root - DEBUG - Tray icon dark
2025-08-03 16:26:17,363 - config.settings - DEBUG - Saving settings:
2025-08-03 16:26:17,364 - config.settings - DEBUG -   mode: dev
2025-08-03 16:26:17,365 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-03 16:26:17,366 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-03 16:26:17,367 - config.settings - DEBUG - Created directory: dist\dev
2025-08-03 16:26:17,370 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-03 16:27:33,059 - root - DEBUG - triggered hotkey
2025-08-03 16:27:33,060 - root - DEBUG - Hotkey pressed
2025-08-03 16:27:33,062 - root - DEBUG - Cancelling current provider's request
2025-08-03 16:27:33,065 - root - DEBUG - Showing popup window
2025-08-03 16:27:33,082 - root - DEBUG - Clipboard backup: "https://aim4truth.org/2025/05/28/the-hidden-hand-unraveling-the-rothschilds-israel/?utm_source=substack&utm_medium=email" (sleep: 0.2s)
2025-08-03 16:27:33,086 - root - DEBUG - Simulating Ctrl+C
2025-08-03 16:27:33,302 - root - DEBUG - Waited 0.2s for clipboard
2025-08-03 16:27:33,306 - root - DEBUG - Selected text: "cours"
2025-08-03 16:27:33,308 - root - DEBUG - Creating new popup window
2025-08-03 16:27:33,311 - root - DEBUG - Initializing CustomPopupWindow
2025-08-03 16:27:33,312 - root - DEBUG - Setting up CustomPopupWindow UI
2025-08-03 16:27:33,342 - root - DEBUG - Loading actions from unified settings
2025-08-03 16:27:33,345 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,346 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,347 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,348 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,350 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,351 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,353 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,355 - root - DEBUG - DraggableButton initialized
2025-08-03 16:27:33,362 - root - DEBUG - CustomPopupWindow UI setup complete
2025-08-03 16:27:33,377 - root - DEBUG - Cursor is on screen: \\.\DISPLAY1
2025-08-03 16:27:33,378 - root - DEBUG - Screen geometry: PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-03 16:27:33,446 - root - DEBUG - Popup window moved to position: (527, 167)
2025-08-03 16:27:36,565 - root - DEBUG - Entering edit mode
2025-08-04 18:37:29,623 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-04 18:37:29,624 - config.settings - DEBUG - SettingsManager initialized in mode: dev
2025-08-04 18:37:29,625 - config.settings - DEBUG - SettingsManager initialized:
2025-08-04 18:37:29,626 - config.settings - DEBUG -   base_dir: .
2025-08-04 18:37:29,626 - config.settings - DEBUG -   mode: dev
2025-08-04 18:37:29,628 - config.settings - DEBUG -   config_dir: config
2025-08-04 18:37:29,629 - config.settings - DEBUG - Using dev data file: dist\dev\data_dev.json
2025-08-04 18:37:29,629 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:37:29,631 - config.settings - DEBUG - Loaded user data from dist\dev\data_dev.json
2025-08-04 18:37:29,631 - config.settings - DEBUG - Merging system data: {'api_key': '', 'provider': 'Gemini (Recommended)', 'model': '', 'hotkey': 'ctrl+space', 'theme': 'gradient', 'language': 'en', 'auto_update': True, 'run_mode': 'dev', 'ollama_base_url': 'http://localhost:11434', 'ollama_model': '', 'ollama_keep_alive': '5', 'mistral_base_url': 'https://api.mistral.ai/v1', 'mistral_model': '', 'anthropic_model': '', 'openai_base_url': 'https://api.openai.com/v1', 'openai_model': ''}
2025-08-04 18:37:29,632 - root - DEBUG - Unified settings loaded successfully
2025-08-04 18:37:30,062 - root - DEBUG - Providers configured, setting up hotkey and tray icon
2025-08-04 18:37:30,063 - root - DEBUG - Selected provider: Gemini (Recommended)
2025-08-04 18:37:30,065 - root - DEBUG - Mapped provider name: Gemini (Recommended)
2025-08-04 18:37:30,066 - root - DEBUG - Current provider: Gemini (Recommended)
2025-08-04 18:37:30,067 - root - DEBUG - Provider config: {'api_key': 'AIzaSyBgwR_NL6_C0obinXXl2nVDXJUL1d3Zlvs', 'model': '', 'model_name': 'gemini-2.5-flash'}
2025-08-04 18:37:30,067 - root - DEBUG - Provider config loaded successfully
2025-08-04 18:37:30,068 - root - DEBUG - Creating system tray icon
2025-08-04 18:37:30,147 - root - DEBUG - Tray icon dark
2025-08-04 18:37:30,162 - root - DEBUG - Tray icon displayed
2025-08-04 18:37:30,163 - root - DEBUG - Registering hotkey
2025-08-04 18:37:30,163 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-04 18:37:30,185 - root - DEBUG - Hotkey registered
2025-08-04 18:37:30,188 - root - DEBUG - Tray icon dark
2025-08-04 18:37:30,765 - config.settings - DEBUG - Saving settings:
2025-08-04 18:37:30,766 - config.settings - DEBUG -   mode: dev
2025-08-04 18:37:30,766 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:37:30,767 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:37:30,769 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:37:30,770 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:37:41,522 - root - DEBUG - Showing settings window
2025-08-04 18:37:58,672 - root - DEBUG - Showing settings window
2025-08-04 18:38:10,106 - config.settings - DEBUG - Saving settings:
2025-08-04 18:38:10,107 - config.settings - DEBUG -   mode: dev
2025-08-04 18:38:10,107 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:38:10,108 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:38:10,109 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:38:10,111 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:38:10,114 - config.settings - DEBUG - Saving settings:
2025-08-04 18:38:10,115 - config.settings - DEBUG -   mode: dev
2025-08-04 18:38:10,116 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:38:10,116 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:38:10,119 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:38:10,122 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:38:10,124 - config.settings - DEBUG - Saving settings:
2025-08-04 18:38:10,124 - config.settings - DEBUG -   mode: dev
2025-08-04 18:38:10,125 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:38:10,125 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:38:10,127 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:38:10,129 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:38:10,131 - config.settings - DEBUG - Saving settings:
2025-08-04 18:38:10,132 - config.settings - DEBUG -   mode: dev
2025-08-04 18:38:10,132 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:38:10,134 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:38:10,136 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:38:10,139 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:38:10,140 - root - DEBUG - Registering hotkey
2025-08-04 18:38:10,141 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-04 18:38:10,159 - root - DEBUG - Hotkey registered
2025-08-04 18:39:16,196 - root - DEBUG - Showing settings window
2025-08-04 18:39:27,773 - config.settings - DEBUG - Saving settings:
2025-08-04 18:39:27,774 - config.settings - DEBUG -   mode: dev
2025-08-04 18:39:27,775 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:39:27,776 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:39:27,776 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:39:27,779 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:39:27,780 - config.settings - DEBUG - Saving settings:
2025-08-04 18:39:27,782 - config.settings - DEBUG -   mode: dev
2025-08-04 18:39:27,782 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:39:27,783 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:39:27,785 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:39:27,787 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:39:27,790 - config.settings - DEBUG - Saving settings:
2025-08-04 18:39:27,791 - config.settings - DEBUG -   mode: dev
2025-08-04 18:39:27,792 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:39:27,792 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:39:27,793 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:39:27,796 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:39:27,797 - config.settings - DEBUG - Saving settings:
2025-08-04 18:39:27,798 - config.settings - DEBUG -   mode: dev
2025-08-04 18:39:27,798 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-04 18:39:27,799 - config.settings - DEBUG -   data_file.parent: dist\dev
2025-08-04 18:39:27,801 - config.settings - DEBUG - Created directory: dist\dev
2025-08-04 18:39:27,805 - config.settings - DEBUG - Settings saved to dist\dev\data_dev.json
2025-08-04 18:39:28,584 - root - DEBUG - Registering hotkey
2025-08-04 18:39:28,585 - root - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-04 18:39:28,604 - root - DEBUG - Hotkey registered
