2025-08-11 05:16:49,413 - config.settings - DEBUG - File logging enabled: dist\dev\dev_debug.log
2025-08-11 05:16:49,413 - config.settings - DEBUG - SettingsManager initialized:
2025-08-11 05:16:49,413 - config.settings - DEBUG -   base_dir: .
2025-08-11 05:16:49,413 - config.settings - DEBUG -   mode: dev
2025-08-11 05:16:49,419 - config.settings - DEBUG -   config_dir: config
2025-08-11 05:16:49,421 - config.settings - DEBUG -   data_file: dist\dev\data_dev.json
2025-08-11 05:16:49,421 - config.settings - DEBUG - No settings file found at dist\dev\data_dev.json, using defaults
2025-08-11 05:16:49,423 - WritingToolApp - DEBUG - Unified settings loaded successfully
2025-08-11 05:16:49,565 - WritingToolApp - DEBUG - First launch detected (no providers configured), showing onboarding
2025-08-11 05:16:49,565 - WritingToolApp - DEBUG - Showing onboarding window
2025-08-11 05:16:49,568 - WritingToolApp - DEBUG - Synchronized colorMode with saved setting: auto
2025-08-11 05:16:49,574 - root - DEBUG - Initializing onboarding UI
2025-08-11 05:16:52,839 - root - INFO - Received SIGINT. Exiting...
2025-08-11 05:16:52,840 - root - DEBUG - Stopping the listener
2025-08-11 05:16:52,840 - root - DEBUG - Exiting application
2025-08-11 05:16:52,841 - WritingToolApp - DEBUG - Onboarding window closed, continuing with app initialization
2025-08-11 05:16:52,843 - root - DEBUG - Creating system tray icon
2025-08-11 05:16:52,843 - root - DEBUG - Icon path resolved to: 
2025-08-11 05:16:52,843 - root - WARNING - Tray icon not found at 
2025-08-11 05:16:52,851 - root - DEBUG - Tray icon dark
2025-08-11 05:16:52,859 - root - DEBUG - Tray icon show() called
2025-08-11 05:16:52,859 - root - DEBUG - Tray icon confirmed visible after 1 attempts
2025-08-11 05:16:52,859 - root - DEBUG - Tray icon setup completed
2025-08-11 05:16:52,861 - WritingToolApp - DEBUG - Registering hotkey
2025-08-11 05:16:52,861 - WritingToolApp - DEBUG - Registering global hotkey for shortcut: <ctrl>+<space>
2025-08-11 05:16:52,884 - WritingToolApp - DEBUG - Hotkey registered
2025-08-11 05:16:52,886 - root - DEBUG - Tray icon dark
2025-08-11 05:19:02,663 - __main__ - INFO - App created successfully
2025-08-11 05:19:02,676 - __main__ - INFO - Tray icon exists: False
2025-08-11 05:19:02,678 - __main__ - INFO - Application is running. Check your system tray!
2025-08-11 05:19:02,680 - __main__ - INFO - Press Ctrl+C to exit...
